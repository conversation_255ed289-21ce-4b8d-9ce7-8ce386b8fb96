
## 项目整体架构
##### Module间依赖关系图
[^图注]:depends on
[^ ]:————————————————>

[^ ]:maybe could depends on
[^ ]:======================>

                                                                     {projectId}-integration
                                                                             /|\          \\
                                                                              |            \\
                                                                              |             \\
                                                                              |              \\
                                                                              |               \\
                                                                              |                \\
                                                                              |                 \\ (可以直接依赖)
                                                                              |                  \\
                                                                              |                   \\
                                                                              |                    \\
                                                                              |                     \\
                                                                              |                      \\
                                                                              |                       \\/
{projectId}-web / {projectId}-msg / {projectId}-task————————————————>{projectId}-shared——————————>{projectId}-core————————>{projectId}-dao
                                                                             /|\                      //\
                                                                              |                      //
                                                                              |                     //
                                                                              |                    //
                                                                              |                   //
                                                                              |                  //
                                                                              |                 // (可以直接依赖)
                                                                              |                //
                                                                              |               //
                                                                              |              //
                                                                              |             //
                                                                              |            //
                                                                              |           //
                                                                     {projectId}-facadeimpl
                                                                              |
                                                                              |
                                                                              |
                                                                              |
                                                                             \|/
                                                                     {projectId}-facade

##### 具体模块用途
- **Main Module**：作为整个项目的启动模块，包含所有项目启动配置（application.properties, servyconf.properties(iris), logback.xml(log), sqlmapconfig.xml(dao), etc.），并且依赖其余所有子模块，执行`mvn packge -DskipTests`打包命令，将在target目录下生成{appcode}.jar成果包。
- **Dao Module**：持久化层模块，该模块与数据库交互，定义所有的sql操作，建议只被**Core Module**引用。
- **Core Module**：业务核心模块，该模块直接依赖**Dao Module**，一般用于对crud操作做业务包装或者定义简单直接的业务操作，并在**Shared Module**中进一步组装复杂业务。如果**Shared Module**被省略，则RocketMq的消息发送接口(producer Interface)在该模块下声明。
- **Shared Module**：业务逻辑及公共模块，该模块直接依赖**Core Module**，一般用于实现复杂的业务逻辑，将**Core Module**中的简单操作进行组装实现，当然，如果项目涉及业务足够简单，那就可以省略该模块，由**Core Module**来替代**Shared Module**，另外RocketMq的消息发送接口(producer Interface)也在该模块下声明。
- **Integration Module**：RPC服务引用模块，该模块主要负责所有外部RPC服务的引用包装，并对项目内模块提供接口方法，一般认为由**Shared Module** or **Core Module**直接引用。
- **Facade Module**：RPC服务接口定义模块，该模块负责所有对外提供的RPC服务接口定义。在该模块下执行`mvn deploy -DskipTests`发布命令或者在发布大盘提测打包，将会把该模块打包上传至远程仓库，供其他业务团队使用，故需要格外注意该接口包的**版本号控制及打包时间点**，以免影响正在使用中的业务方。
- **Facadeimpl Module**：RPC服务接口实现模块，该模块负责实现**Facade Module**中定义的所有接口，建议仅依赖**Shared Module**和**Facade Module**。
- **Web Module**：Web服务模块，该模块直接对外提供web服务，建议直接依赖**Shared Module**。
- **Msg Module**：消息消费模块，该模块下定义了所有Rocket Mq消费方法，建议直接依赖**Shared Module**。
- **Task Module**：任务执行模块，该模块下定义了所有LTS任务的执行方法，建议直接依赖**Shared Module**。
- **Test Module**：单元测试模块，该模块下实现所有的项目单元测试。之前的脚手架中尝试将该模块独立出来，但由此会产生许多不必要的冗余代码及配置，抑或是打包过程中无法直接依赖**Main Module**而失败，因此，在这一版脚手架中，直接将**Test Module**整合进**Main Module**，规避上述问题。
- **Others**：上述模块内容及相互之间的依赖关系是建立在一个标准的业务项目上讨论的，希望使用者尽量**规范简化模块间的依赖，但这并不是强制要求**，并且因为业务上复杂度的不可预见性，使用者完全可以在具体使用过程中对项目结构重新改造以适应具体情况。
> 「应用模块」中相关接口及其实现类命名规范见「命名规范」
##### 服务接口Request&Response
- **HTTP**：
   - 请求：所有使用17Boot框架的服务发起的HTTP请求建议使用框架提供的请求客户端来进行HTTP访问（**17boot-utilities**中的`cn.com.servyou.i7boot.util.http.RestTemplateBuilder`，主要涉及服务间的**「链路追踪」** 及 **「调用日志记录」**）；
   - 响应：所有HTTP请求响应体建议使用`cn.com.servyou.xqy.framework.web.rest.ApiResponse`，分页查询相关结果推荐使用`ApiResponse<cn.com.servyou.i7boot.core.Pagination<xxxVo>>`进行结果包装。
- **RPC**：
   - 请求：无明确要求，不过这里强烈建议<font color=Red>不要在DUBBO数据交互中使用枚举类</font>，调用双方枚举不同步时会导致DUBBO数据包解析报错；
   - 响应：所有DUBBO响应体建议使用`[cn.com.servyou.xqy.framework.rpc.facade.VoidResult, cn.com.servyou.xqy.framework.rpc.facade.SingleResult, cn.com.servyou.xqy.framework.rpc.facade.ListResult]`进行结果包装。
- **MQ**：
   - 发送：无明确要求，一般建议调用produer发送前将请求对象转为JSON格式String发送；
   - 消费：建议使用`com.servyou.elephant.mq.Acknowledge`明确返回的消费结果，避免不必要的重试消费。
- **LTS**：
   - 任务提交：无明确要求；
   - 任务执行：建议使用`cn.com.servyou.xqy.framework.job.Action`明确返回的执行结果，另外建议任务执行线程能够有计划的**响应线程中断信号**，以此可以通过外部干预来终止长时间死任务。
- **Others**：其他服务间数据交互，建议实现Trace Log的传递以保证调用链完整，执行步骤为：
   1. ``` java
     String traceId = TraceLogContext.currentTraceLogContext().getTraceId();//当前调用者使用的traceId
     String rpcId = TraceLogContext.currentTraceLogContext().nextChildContextRpcId();//生成用于接下去使用的调用序列ID
     ```
   2. 将上述traceId和rpcId传递给被调用方；
   3. 被调用方在获得上述信息后，将这两个内容置入当前线程变量
        ``` java
        TraceLogContext.currentTraceLogContext().setTraceId(traceIdFromInvoker);
        TraceLogContext.currentTraceLogContext().setRpcId(rpcIdFromInvoker);
        ```
> 「服务接口」中涉及的对象命名规范见「命名规范」
##### 命名规范
下面以Valut服务中密钥（Secret）相关做示例：
- **Dao**：一般的，将Dao层接口命名为*SecretDAO*，对象命名为*SecretDO（Secret Database Object）*；
- **Core&Shared**：**Core Module**下接口服务一般命名为*SecretService*，接口实现为*SecretServiceImpl*。**Shared Module**下服务命名可以同**Core Module**，也可以为*SecretManagement*，对应实现类为*SecretManagementImpl*。所有只在项目内部核心传递的参数直接使用*Secret*，不添加任何后缀。
- **Web**：一般的，将WEB层对外接口命名为*SecretController*，对象命名为*SecretVO（Secret View Object）*；
- **对外服务**：一般来说，需要对外提供服务或者接收的对象命名为*SecretDTO（Secret Data Transport Object）*，包括但不限于DUBBO、ROCKET MQ、LTS。
   - **Facade Module**下接口命名为*SecretFacade*，
   - **Facadeimpl Module**下命名为*SecretFacadeImpl*，
   - **Integration Module**下服务类命名为*SecretFacadeClient*和*SecretFacadeClientImpl*，
   - **Shared Module**下消息发送服务命名为*SecretProducer*，
   - **Msg Module**下消息消费服务命名为*SercetConsumer*，
   - **Task Module**下任务执行服务命名为*SecretTaskRunner*。
> 以上所有对象除仅在内部使用的，其余对象必须实现序列化（implements Serializable）
##### 打包发布规范
近段时间发现开发者在发布团队成果包时，并不注重依赖的清理维护，导致使用者在不知情的情况下额外引入了不必要的Jar包，期间甚至导致了服务引用方的整体不可用，这是十分危险的，所以希望开发者能够多注意成果包的依赖构成，针对服务提供者，建议关注依赖的作用域<scope>属性，尽可能的减小包的体积，去除非必要的依赖；而针对服务使用方，建议关注引用时引起的项目依赖变化，尤其关注发生的<font color=Red>依赖冲突以及17boot框架与xqy框架的不可共存</font>，推荐使用Maven Helper等IDEA插件帮助检查。
- **Facade打包**：作为最常用的对外依赖，理想状态下，**Facade Module**在mvn结构中独立于父POM，可以单独打包发布，所有额外依赖作用域都为provided，这也意味着第三方应用在引入该Facade包时<font color=Red>不会带来任何额外依赖引入</font>。
- **SDK打包**：原则上希望对外提供的SDK做到高内聚，避免版本依赖；打包前请检查依赖目录，去除可能会给服务引用方带来依赖上的未知变更或者对版本要求并不敏感的依赖项*（例如spring-xxx等spring相关依赖，xyq-xxx、17boot-xxx等框架依赖，apache-commons-xxx、google-xxx等工具类依赖）*，将这些依赖引入交给服务引用方来做。对无法避免版本编码差异的，也应尽量做好多版本适应，建议使用`ClassUtils.isPresent`或者`@Conditional`等工具。
##### 版本控制
- 上面**Facade Module**中提到了注意版本控制，脚手架生成的项目pom中默认将项目版本定为1.0.0-SNAPSHOT，一般希望项目所有模块版本号跟随父模块，<**Facade Module**在脚手架中独立于Parent Pom，所以版本号也需要独立修改>。在日常的迭代开发中，建议将版本号与迭代分支号统一。推荐的较为便捷的版本更新命令为`mvn versions:set -DnewVersion=1.1.0-SNAPSHOT`。
