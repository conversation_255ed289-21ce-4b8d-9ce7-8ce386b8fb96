<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.com.servyou</groupId>
    <artifactId>tax-asynctask</artifactId>
    <version>${version.number}-${build.number}</version>
    <packaging>pom</packaging>

    <!-- module aggregate -->
    <modules>
        <module>tax-asynctask-facade</module>
        <module>tax-asynctask-facadeimpl</module>
        <module>tax-asynctask-integration</module>
        <module>tax-asynctask-main</module>
        <module>tax-asynctask-core</module>
        <module>tax-asynctask-shared</module>
        <module>tax-asynctask-dao</module>
        <module>tax-asynctask-msg</module>
        <module>tax-asynctask-web</module>
        <module>tax-asynctask-common</module>
        <module>tax-asynctask-test</module>
    </modules>

    <!-- global config -->
    <properties>
        <version.number>20250828_partition</version.number>
        <build.number>SNAPSHOT</build.number>
        <java.version>1.8</java.version>
        <spring.framework.version>5.1.9.RELEASE</spring.framework.version>
        <springboot.version>2.1.18.RELEASE</springboot.version>
        <!-- 17boot version -->
        <i7boot.version>3.3.0-SNAPSHOT</i7boot.version>
        <!-- custome version-->
        <taxbase.version>20250729-SNAPSHOT</taxbase.version>
        <taxcore.version>20250327-SNAPSHOT</taxcore.version>
        <taxgateway.version>20250828-SNAPSHOT</taxgateway.version>
        <taxlineup.version>20241226-SNAPSHOT</taxlineup.version>
        <mvel2.version>2.4.4.Final</mvel2.version>
    </properties>

    <!-- version arbitration -->
    <dependencyManagement>
        <dependencies>
            <!-- spring dependencies -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.framework.version}</version>
            </dependency>
            <!-- spring dependencies -->

            <!-- 17boot dependencies -->
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-dubbo-rpc-facade</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-test</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-iris-springboot</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-mybatis-springboot</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-log</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-web</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-xframework-model</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-mq-springboot</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-dubbo-api</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-security</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-bootstrap</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-web-support</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-iris-api</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>2.1.21.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>17boot-cache-springboot</artifactId>
                <version>${i7boot.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.1.3</version>
            </dependency>
            <!-- 17boot dependency end -->

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <!-- framework dependencies -->

            <!-- application dependencies -->
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-facadeimpl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-shared</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-msg</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-asynctask-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- application dependencies -->

            <!-- other dependencies -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.47</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <artifactId>fastjson</artifactId>
                <groupId>com.alibaba</groupId>
                <version>1.2.70</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.7</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.11</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>20.0</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.8.3</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-digester</groupId>
                <artifactId>commons-digester</artifactId>
                <version>1.8</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.4</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.5</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.3</version>
            </dependency>
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.8</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.19.0-GA</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.16</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.16.14</version>
                <optional>true</optional>
            </dependency>
            <!-- other dependencies -->

            <!-- custom dependencies -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.0.M3</version>
            </dependency>
            <dependency>
                <artifactId>fastjson</artifactId>
                <groupId>com.alibaba</groupId>
                <version>1.2.70</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.21.3</version>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>taxbase-facade</artifactId>
                <version>${taxbase.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.com.servyou</groupId>
                        <artifactId>tax-gateway-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.com.servyou</groupId>
                        <artifactId>tax-asynctask-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>xqy-taxcore-facade</artifactId>
                <version>${taxcore.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>tax-gateway-facade</artifactId>
                <version>${taxgateway.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.com.servyou</groupId>
                        <artifactId>tax-asynctask-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.com.servyou</groupId>
                <artifactId>taxlineup-facade</artifactId>
                <version>${taxlineup.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>${mvel2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>20.0</version>
            </dependency>
            <!-- custom dependencies -->
        </dependencies>

    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>

    <!-- 构建管理 -->
    <build>
        <!-- 插件 -->
        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <compilerVersion>${java.version}</compilerVersion>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <!-- prevents endPosTable exception for maven compile -->
                    <useIncrementalCompilation>false</useIncrementalCompilation>
                </configuration>
            </plugin>
            <!-- 打包源码插件,打包完成后会生成一份源码.jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--
                pom精简插件:
                将pom.xml压缩为flattened-pom.xml,然后在install和deploy阶段使用flattened-pom.xml替代pom.xml.
                具体压缩策略如下:
                1.和构建有关的元素会被删除;
                2.和开发有关的元素默认会被删除;
                3.只包含构件的使用者必须的一些信息;
                4.变量会被解析;
                5.上级关系会被解析,然后被压缩删除;
                6.构建时实际使用的profile会被评估,视情况处理;
                7.由JDK或者OS驱动的profile会被保留,需要时可以动态地控制依赖.
                更多请查看http://www.mojohaus.org/flatten-maven-plugin/plugin-info.html
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.0.0</version>
                <executions>
                    <execution>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.1</version>
            </plugin>
        </plugins>
    </build>

    <!-- 构件管理 -->
    <distributionManagement>
        <!-- release仓库 -->
        <repository>
            <id>xqy-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://maven.dc.servyou-it.com/nexus/content/repositories/xqy/</url>
        </repository>
        <!-- snapshot仓库 版本尾缀为-SNAPSHOT的组件会从该地址上传或下载 -->
        <snapshotRepository>
            <id>xqy-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://maven.dc.servyou-it.com/nexus/content/repositories/xiaoyiqe-test/</url>
        </snapshotRepository>
    </distributionManagement>

</project>