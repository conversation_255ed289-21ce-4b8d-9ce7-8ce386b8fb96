package cn.com.servyou.asynctask.common.dto;

import java.io.Serializable;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/11 16:37
 */
public class ResultNotifyReqDTO implements Serializable {
    private static final long serialVersionUID = -5010003169120001583L;

    /**
     * 异步任务系统-任务ID
     */
    private Long asyncTaskId;

    private boolean success;
    /**
     * 业务参数
     */
    private String bizParams;
    /**
     * 执行结果
     */
    private Object data;

    private String errorCode;
    private String message;
    /**
     * 是否可重试
     */
    private boolean isRetryAble;
    /**
     * 是否排队阶段错误
     */
    private boolean isLineupAPIError;

    /**
     * 任务代码
     */
    private String taskCode;
    /**
     * 业务方编码
     */
    private String bizPartCode;
    /**
     * 业务编码
     */
    private String bizCode;

    public ResultNotifyReqDTO() {
    }


    public Long getAsyncTaskId() {
        return asyncTaskId;
    }

    public void setAsyncTaskId(Long asyncTaskId) {
        this.asyncTaskId = asyncTaskId;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getBizParams() {
        return bizParams;
    }

    public void setBizParams(String bizParams) {
        this.bizParams = bizParams;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isRetryAble() {
        return isRetryAble;
    }

    public void setRetryAble(boolean retryAble) {
        isRetryAble = retryAble;
    }

    public String getBizPartCode() {
        return bizPartCode;
    }

    public void setBizPartCode(String bizPartCode) {
        this.bizPartCode = bizPartCode;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public boolean isLineupAPIError() {
        return isLineupAPIError;
    }

    public void setLineupAPIError(boolean lineupAPIError) {
        isLineupAPIError = lineupAPIError;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }
}
