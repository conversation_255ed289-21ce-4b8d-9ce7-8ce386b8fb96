package cn.com.servyou.asynctask.common.dto;

import java.io.Serializable;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/11 17:00
 */
public class ResultNotifyRespDTO implements Serializable {
    private static final long serialVersionUID = 6549730080644002795L;
    /**
     * 异步任务系统-任务ID
     */
    private Long asyncTaskId;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 失败原因
     */
    private String errorCode;
    private String message;

    public ResultNotifyRespDTO() {
    }


    public ResultNotifyRespDTO(Long asyncTaskId, boolean success, String errorCode, String message) {
        this.asyncTaskId = asyncTaskId;
        this.success = success;
        this.errorCode = errorCode;
        this.message = message;
    }

    public static ResultNotifyRespDTO successful(Long asyncTaskId) {
        return new ResultNotifyRespDTO(asyncTaskId, true, null, null);
    }

    public static ResultNotifyRespDTO failed(Long asyncTaskId, String errorCode, String message) {
        return new ResultNotifyRespDTO(asyncTaskId, false, errorCode, message);
    }

    public Long getAsyncTaskId() {
        return asyncTaskId;
    }

    public void setAsyncTaskId(Long asyncTaskId) {
        this.asyncTaskId = asyncTaskId;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
