package cn.com.servyou.asynctask.core.common;

import cn.com.servyou.asynctask.core.domain.AsyncTaskCheckRetryExcDTO;
import cn.com.servyou.asynctask.core.utils.AssertUtil;
import cn.com.servyou.asynctask.core.utils.MD5Util;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.mvel2.MVEL;
import org.mvel2.ParserContext;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.tools.Tool;
import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/18 15:33
 */
@Slf4j
@Service
public class Mvel2ScriptServiceImpl implements Mvel2ScriptService, InitializingBean {

    static {
        System.setProperty("mvel2.invoked_meth_exceptions_bubble", "true");
    }

    private Cache<String, Serializable> elCache;
    private ParserContext ctx;

    @Override
    public void afterPropertiesSet() throws Exception {
        //初始化基础变量工程
        elCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(24, TimeUnit.HOURS)
                .build();
        //初始化解析上下文
        ctx = new ParserContext();
    }

    @Override
    public <T> T execute(AsyncTaskCheckRetryExcDTO asyncTaskCheckRetryExcDTO) {
        if (StrUtil.isNotEmpty(asyncTaskCheckRetryExcDTO.getScript())) {
            String script = asyncTaskCheckRetryExcDTO.getScript();
            Map<String, Object> variables = asyncTaskCheckRetryExcDTO.getVariables();
            try {
                String md5 = MD5Util.calculateMD5(script);
                Serializable s = elCache.getIfPresent(md5);
                if (s == null) {
                    long start = System.currentTimeMillis();
                    s = MVEL.compileExpression(script, ctx);
                    log.info("mvel 编译耗时{}", System.currentTimeMillis() - start);
                    elCache.put(md5, s);
                }
                long start = System.currentTimeMillis();
                Object o = MVEL.executeExpression(s, variables);
                log.info("mvel 运行耗时{}", System.currentTimeMillis() - start);
                ScheduleLogUtils.log("任务执行异步回调结果", asyncTaskCheckRetryExcDTO.getBizTaskId(), asyncTaskCheckRetryExcDTO.getCustomerId(), "MVEL script={},variables={} 执行成功, result={}", script, JSON.toJSONString(variables), o);
                return (T) o;

            } catch (Exception ex) {
                log.error("mvel  脚本执行失败", ex);
                ScheduleLogUtils.log("任务执行异步回调结果", asyncTaskCheckRetryExcDTO.getBizTaskId(), asyncTaskCheckRetryExcDTO.getCustomerId(), "MVEL script={},variables={} 执行失败", script, JSON.toJSONString(variables));
                throw ex;
            }
        }
        ScheduleLogUtils.log("任务执行异步回调结果", asyncTaskCheckRetryExcDTO.getBizTaskId(), asyncTaskCheckRetryExcDTO.getCustomerId(), "MVEL 脚本为空, result=null");
        return null;
    }
}
