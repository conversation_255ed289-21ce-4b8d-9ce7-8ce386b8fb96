package cn.com.servyou.asynctask.core.common;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.mvel2.MVEL;
import org.mvel2.ParserContext;
import org.mvel2.integration.VariableResolverFactory;
import org.mvel2.integration.impl.MapVariableResolverFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

/**
 * description: mvel脚本解析 <br>
 * date: 2020-03-84 03:30:30 <br>
 * author: xzs <br>
 * version: 1.0 <br>
 */
@Service
@Slf4j(topic = "SCRIPT-DIGEST")
public class MvelScriptServiceImpl implements InitializingBean {
    private static final String EXPRESSION = "import cn.com.servyou.xqy.gateway.utils.GatewayUtil;\n" +
            "import cn.com.servyou.xqy.gateway.utils.RSAEncryptUtil;\n" +
            "import cn.com.servyou.xqy.gateway.utils.ServyouRestAesAndBase64Util;";

    static {
        System.setProperty("mvel2.invoked_meth_exceptions_bubble", "true");
    }


    private VariableResolverFactory varFactory;
    private Cache<String, Serializable> elCache;

    private ParserContext ctx;


    public <T> T exec(String script, Object context) {

        if (StrUtil.isNotEmpty(script)) {
            String md5 = DigestUtil.md5Hex(script);
            Serializable s = elCache.getIfPresent(md5);
            if (s == null) {
                s = MVEL.compileExpression(script, ctx);
                elCache.put(md5, s);
            }
            try {
                VariableResolverFactory instanceFactory = new MapVariableResolverFactory();
                instanceFactory.setNextFactory(varFactory);
                return (T) MVEL.executeExpression(s, context, instanceFactory);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
                throw ex;
            }
        }
        return null;
    }


    @Override
    public void afterPropertiesSet() throws Exception {

        //初始化基础变量工程
        VariableResolverFactory functionFactory = new MapVariableResolverFactory();

       // MVEL.eval(EXPRESSION, functionFactory);
        functionFactory.createVariable("log", log);
        varFactory = new MapVariableResolverFactory();
        varFactory.setNextFactory(functionFactory);


        elCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .build();

        //初始化解析上下文
        ctx = new ParserContext();
    }
}
