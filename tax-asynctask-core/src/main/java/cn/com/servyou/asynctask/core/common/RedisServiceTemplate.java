package cn.com.servyou.asynctask.core.common;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/12/20 13:39
 */
public interface RedisServiceTemplate {

    <T> T executeSingle(final String cacheKey, final String hashKey, final Class clazz,
                        final SingleServiceCallback<T> redisServiceCallback);

    <T> T executeSingle(final String cacheKey, final Class clazz, long timeout, TimeUnit timeUnit,
                        final SingleServiceCallback<T> redisServiceCallback);


    <T> List<T> executeList(final String cacheKey, final String hashKey, final Class clazz,
                            final ListServiceCallback<T> redisServiceCallback);


    <T> T executeString(final String cacheKey, final String hashKey,
                        final RedisServiceCallback<T> redisServiceCallback);
}
