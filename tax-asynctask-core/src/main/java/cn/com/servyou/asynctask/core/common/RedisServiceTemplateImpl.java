package cn.com.servyou.asynctask.core.common;

import cn.com.servyou.asynctask.core.service.RedisService;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description 有缓存走缓存，无缓存查库并更新缓存，库里也没有缓存空字符串，防止缓存穿透对数据库产生压力，
 * <AUTHOR>
 * @Date 2023/12/20 13:41
 */
@Slf4j
@Service
public class RedisServiceTemplateImpl implements RedisServiceTemplate {
    @Autowired
    private RedisService redisService;
    @Autowired
    private RedissonClient redissonClient;


    @Override
    public <T> T executeSingle(String cacheKey, String hashKey, Class clazz,
                               SingleServiceCallback<T> redisServiceCallback) {
        String cacheValue = this.redisService.hget(cacheKey, hashKey);
        if ("".equals(cacheValue)) {
            log.debug("executeSingle走缓存 无配置;{},{}", cacheKey, hashKey);
            return null;
        }
        if (StringUtils.isNotBlank(cacheValue)) {
            log.debug("executeSingle走缓存 有配置;{},{}", cacheKey, hashKey);
            return (T) JSONUtil.toBean(cacheValue, clazz);
        }
        RLock lock = null;
        try {
            // 取锁
            lock = redissonClient.getLock("LOCK-" + cacheKey + "-" + hashKey);
            lock.lock(3, TimeUnit.SECONDS);

            log.debug("executeSingle取到锁,lock={}", lock.isLocked());
            // 再查redis
            cacheValue = this.redisService.hget(cacheKey, hashKey);
            if ("".equals(cacheValue)) {
                log.debug("executeSingle走重试缓存 无配置;{},{}", cacheKey, hashKey);
                return null;
            }
            if (StringUtils.isNotBlank(cacheValue)) {
                log.debug("executeSingle走重试缓存 有配置;{},{}", cacheKey, hashKey);
                return (T) JSONUtil.toBean(cacheValue, clazz);
            }
            // 查库
            T dbResult = redisServiceCallback.fetchFromDb();

            if (null != dbResult) {
                log.debug("executeSingle走db 有配置;{},{}", cacheKey, hashKey);
                cacheValue = JSONUtil.toJsonStr(dbResult);
            } else {
                log.debug("executeSingle走db 无配置;{},{}", cacheKey, hashKey);
                cacheValue = "";
            }
            this.redisService.hput(cacheKey, hashKey, cacheValue);
            return dbResult;
        } finally {
            try {
                if (null != lock && lock.isLocked()) {
                    lock.unlock();
                    log.debug("executeSingle unlock");
                }
            } catch (Exception e) {
                log.warn("查询数据库更新缓存释放锁时异常", e);
            }
        }
    }

    @Override
    public <T> T executeSingle(String cacheKey, Class clazz,
                               long timeout, TimeUnit timeUnit, SingleServiceCallback<T> redisServiceCallback) {
        String cacheValue = this.redisService.get(cacheKey);
        if ("".equals(cacheValue)) {
            log.debug("executeSingle走缓存 无配置;{}", cacheKey);
            return null;
        }
        if (StringUtils.isNotBlank(cacheValue)) {
            log.debug("executeSingle走缓存 有配置;{}", cacheKey);
            return (T) JSONObject.parseObject(cacheValue, clazz);
        }
        RLock lock = null;
        try {
            // 取锁
            lock = redissonClient.getLock("LOCK-" + cacheKey);
            lock.lock(5, TimeUnit.SECONDS);

            log.debug("executeSingle取到锁,lock={}", lock.isLocked());
            // 再查redis
            cacheValue = this.redisService.get(cacheKey);
            if ("".equals(cacheValue)) {
                log.debug("executeSingle走重试缓存 无配置;{}", cacheKey);
                return null;
            }
            if (StringUtils.isNotBlank(cacheValue)) {
                log.debug("executeSingle走重试缓存 有配置;{}", cacheKey);
                return (T) JSONObject.parseObject(cacheValue, clazz);
            }
            // 查库
            T dbResult = redisServiceCallback.fetchFromDb();

            if (null != dbResult) {
                log.debug("executeSingle走db 有配置;{}", cacheKey);
                cacheValue = JSONObject.toJSONString(dbResult);
            } else {
                log.debug("executeSingle走db 无配置;{}", cacheKey);
                cacheValue = "";
            }
            this.redisService.set(cacheKey, cacheValue, timeout, timeUnit);
            return dbResult;
        } finally {
            try {
                if (null != lock && lock.isLocked()) {
                    lock.unlock();
                    log.debug("executeSingle unlock");
                }
            } catch (Exception e) {
                log.warn("查询数据库更新缓存释放锁时异常", e);
            }
        }
    }

    @Override
    public <T> List<T> executeList(String cacheKey, String hashKey, Class clazz,
                                   ListServiceCallback<T> redisServiceCallback) {
        String cacheValue = this.redisService.hget(cacheKey, hashKey);
        if ("".equals(cacheValue)) {
            log.debug("executeList走缓存 无配置;{},{}", cacheKey, hashKey);
            return null;
        }
        if (StringUtils.isNotBlank(cacheValue)) {
            log.debug("executeList走缓存 有配置;{},{}", cacheKey, hashKey);
            return JSONUtil.toList(cacheValue, clazz);
        }
        RLock lock = null;
        try {
            // 取锁
            lock = redissonClient.getLock("LOCK-" + cacheKey + "-" + hashKey);
            lock.lock(3, TimeUnit.SECONDS);

            log.debug("executeList取到锁,lock={}", lock.isLocked());
            cacheValue = this.redisService.hget(cacheKey, hashKey);
            if ("".equals(cacheValue)) {
                log.debug("executeList走重试缓存 无配置;{},{}", cacheKey, hashKey);
                return null;
            }
            if (StringUtils.isNotBlank(cacheValue)) {
                log.debug("executeList走重试缓存 有配置;{},{}", cacheKey, hashKey);
                return JSONUtil.toList(cacheValue, clazz);
            }
            List<T> dbResult = redisServiceCallback.fetchFromDb();

            if (null != dbResult && dbResult.size() > 0) {
                log.debug("executeList走db 有配置;{},{}", cacheKey, hashKey);
                cacheValue = JSONUtil.toJsonStr(dbResult);
            } else {
                log.debug("executeList走db 无配置;{},{}", cacheKey, hashKey);
                cacheValue = "";
            }
            this.redisService.hput(cacheKey, hashKey, cacheValue);
            return dbResult;
        } finally {
            try {
                if (null != lock) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.warn("查询数据库更新缓存释放锁时异常", e);
            }
        }
    }

    @Override
    public <T> T executeString(String cacheKey, String hashKey, RedisServiceCallback<T> redisServiceCallback) {
        String cacheValue = this.redisService.hget(cacheKey, hashKey);
        if ("".equals(cacheValue)) {
            log.debug("executeString走缓存 无配置;{},{}", cacheKey, hashKey);
            return null;
        }
        if (StringUtils.isNotBlank(cacheValue)) {
            log.debug("executeString走缓存 有配置;{},{}", cacheKey, hashKey);
            return (T) cacheValue;
        }
        RLock lock = null;
        try {
            // 取锁
            lock = redissonClient.getLock("LOCK-" + cacheKey + "-" + hashKey);
            lock.lock(3, TimeUnit.SECONDS);

            log.debug("executeString取到锁,lock={}", lock.isLocked());
            cacheValue = this.redisService.hget(cacheKey, hashKey);
            if ("".equals(cacheValue)) {
                log.debug("executeString走重试缓存 无配置;{},{}", cacheKey, hashKey);
                return null;
            }
            if (StringUtils.isNotBlank(cacheValue)) {
                log.debug("executeString走重试缓存 有配置;{},{}", cacheKey, hashKey);
                return (T) cacheValue;
            }
            T dbResult = redisServiceCallback.fetchFromDb();

            if (null != dbResult) {
                log.debug("executeString走db 有配置;{},{}", cacheKey, hashKey);
                cacheValue = JSONUtil.toJsonStr(dbResult);
            } else {
                log.debug("executeString走db 无配置;{},{}", cacheKey, hashKey);
                cacheValue = "";
            }
            this.redisService.hput(cacheKey, hashKey, cacheValue);
            return dbResult;
        } finally {
            try {
                if (null != lock) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.warn("查询数据库更新缓存释放锁时异常", e);
            }
        }
    }


}
