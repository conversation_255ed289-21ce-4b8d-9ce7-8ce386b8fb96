package cn.com.servyou.asynctask.core.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.TransactionSystemException;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;

import javax.annotation.Resource;
import java.lang.reflect.UndeclaredThrowableException;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/19 20:51
 */
@Slf4j
@Service
public class TransactionServiceImpl implements TransactionService {

    @Resource
    private DataSourceTransactionManager transactionManager;

    @Override
    public <T> T execute(TransactionDefinition transactionDefinition, TransactionCallback<T> action) throws TransactionException {
        TransactionStatus status = this.transactionManager.getTransaction(transactionDefinition);
        T result;
        try {
            result = action.doInTransaction(status);
        } catch (RuntimeException ex) {
            // Transactional code threw application exception -> rollback
            rollbackOnException(status, ex);
            throw ex;
        } catch (Error err) {
            // Transactional code threw error -> rollback
            rollbackOnException(status, err);
            throw err;
        } catch (Throwable ex) {
            // Transactional code threw unexpected exception -> rollback
            rollbackOnException(status, ex);
            throw new UndeclaredThrowableException(ex, "TransactionCallback threw undeclared checked exception");
        }
        this.transactionManager.commit(status);
        return result;
    }

    @Override
    public <T> T execute(TransactionCallback<T> action) throws TransactionException {
        return this.execute(new DefaultTransactionDefinition(), action);
    }

    @Override
    public <T> T executeNew(TransactionCallback<T> action) throws TransactionException {
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        transactionDefinition.setIsolationLevel(DefaultTransactionDefinition.ISOLATION_DEFAULT);
        return this.execute(transactionDefinition, action);
    }

    /**
     * Perform a rollback, handling rollback exceptions properly.
     *
     * @param status object representing the transaction
     * @param ex     the thrown application exception or error
     * @throws TransactionException in case of a rollback error
     */
    private void rollbackOnException(TransactionStatus status, Throwable ex) throws TransactionException {
        try {
            this.transactionManager.rollback(status);
        } catch (TransactionSystemException ex2) {
            log.error("Application exception overridden by rollback exception", ex);
            ex2.initApplicationException(ex);
            throw ex2;
        } catch (RuntimeException ex2) {
            log.error("Application exception overridden by rollback exception", ex);
            throw ex2;
        } catch (Error err) {
            log.error("Application exception overridden by rollback error", ex);
            throw err;
        }
    }
}

