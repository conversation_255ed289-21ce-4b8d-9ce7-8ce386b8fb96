package cn.com.servyou.asynctask.core.config;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 10:56
 */
@Data
@Component
public class TaskConfig {

    @Value("${asynctask.unqueue.exc:LOGIN_LINEUP_NEEDED}")
    private String unQueuedExc;

    @Value("${asynctask.environmentCode:PROD}")
    private String environmentCode;

    @Value("${asynctask.lineup.tag.suffix:queuedFor}")
    private String lineupTagSuffix;

    @Value("${asynctask.unqueued.exc.num:1}")
    private Integer unQueuedExcNum;


    @Value("${route.config:{\"currentMode\":\"NEW\",\"transitionTime\":1755248400000}}")
    private String routeConfig;

    /**
     * standardDate 标准比较日期
     * cycle 周期
     * rangeTime 范围
     * 若当前时间与标准比较日期计算周期，是周期第一天，且在0点-rangeTime内，则视为临界时间
     */
    @Value("${partition.critical:{\"standardDate\":1754150400000,\"cycle\":7,\"rangeTime\":1800}}")
    private String partitionCritical;

    /**
     * 新表老表路由配置
     */
    @Value("")
    private String route1Config;

    private static final String COLON = ":";
    private static final String DOT = ",";
    private static final String REDIS_KEY_ROOT = "ASYNCTASK";
    private static final String REDIS_KEY_MERGE_ROOT = REDIS_KEY_ROOT + COLON + "MERGE";

    public static String buildMergeKey(String md5) {
        return REDIS_KEY_MERGE_ROOT + COLON + md5;
    }


    public static String buildMergeVal(Long bizTaskId, String customerId) {
        return bizTaskId + DOT + customerId;
    }

    /**
     * key:bizTaskId
     * val:customerId
     *
     * @param value
     * @return
     */
    public static Pair<Long, String> decodeMergeVal(String value) {
        List<String> list = StrUtil.split(value, DOT);
        return new Pair<>(Long.valueOf(list.get(0)), list.get(1));
    }

    public List<String> getUnQueuedExcList() {
        return StrUtil.split(unQueuedExc, DOT);
    }

    /**
     * 未排队可执行次数
     *
     * @return
     */
    public int getUnQueuedExcNumConfig() {
        return unQueuedExcNum == null ? 1 : unQueuedExcNum;
    }

    public RouteConfig getRouteConfig() {
        return StrUtil.isEmpty(routeConfig) ? null : JSON.parseObject(routeConfig, RouteConfig.class);
    }

    public PartitionCriticalConfig getPartitionCritical() {
        return StrUtil.isEmpty(partitionCritical) ? null : JSON.parseObject(partitionCritical, PartitionCriticalConfig.class);
    }

    /**
     * 是否表切换过渡范围内
     * 配置的过渡时间大于等当前时间
     * @return
     */
    public boolean isRouteCritical() {
        RouteConfig config = getRouteConfig();
        if (null == config || null == config.getTransitionTime()) {
            return false;
        }
        return config.getTransitionTime() >= System.currentTimeMillis();
    }

    /**
     * 是否在分区过渡范围内
     * @return
     */
    public boolean isCriticalTime() {
        if (StrUtil.isEmpty(partitionCritical)) {
            return false;
        }
        PartitionCriticalConfig partitionCriticalConfig = JSON.parseObject(partitionCritical, PartitionCriticalConfig.class);
        LocalDate standardDate = Instant.ofEpochMilli(partitionCriticalConfig.getStandardDate()).atZone(ZoneId.systemDefault()).toLocalDate();
        if (isFirstDayOfCycle(standardDate, partitionCriticalConfig.getCycle(), LocalDate.now())) {
            if (getSecondsSinceMidnight() <= partitionCriticalConfig.getRangeTime()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isFirstDayOfCycle(LocalDate standardDate, int cycle, LocalDate inputDate) {

        long daysDiff = ChronoUnit.DAYS.between(standardDate, inputDate);
        return daysDiff % cycle == 0;
    }

    public static long getSecondsSinceMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().atStartOfDay();
        return ChronoUnit.SECONDS.between(midnight, now);
    }

}
