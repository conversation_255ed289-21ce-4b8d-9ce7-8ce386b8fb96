package cn.com.servyou.asynctask.core.convert;

import cn.com.servyou.asynctask.core.domain.Test;
import cn.com.servyou.asynctask.dataobj.TestDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class TestCoreConvert {

    /**
    * TestDO to Test
    *
    * @param testDO TestDO
    * @return Test
    */
    public static Test toTest(TestDO testDO) {
        Test test = new Test();
        if (null == testDO)
        return test;
        test.setId(testDO.getId());
        test.setName(testDO.getName());
        return test;
    }

    /**
    * List<TestDO> to List<Test>
    *
    * @param testDOList List<TestDO>
    * @return List<Test>
    */
    public static List<Test> toList(List<TestDO> testDOList) {
        List<Test> testList = new ArrayList<>();
        if (CollectionUtils.isEmpty(testDOList))
            return testList;
        for (TestDO testDO : testDOList) {
            testList.add(toTest(testDO));
        }
        return testList;
    }

}
