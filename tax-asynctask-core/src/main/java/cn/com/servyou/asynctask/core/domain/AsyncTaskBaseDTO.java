package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/17 20:23
 */
@Data
public class AsyncTaskBaseDTO extends DTO {
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 任务id
     */
    private Long bizTaskId;

    public AsyncTaskBaseDTO() {
    }

    public AsyncTaskBaseDTO(String customerId, Long bizTaskId) {
        this.customerId = customerId;
        this.bizTaskId = bizTaskId;
    }
}
