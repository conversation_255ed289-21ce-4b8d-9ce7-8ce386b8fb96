package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

import java.util.Map;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/18 16:03
 */
@Data
public class AsyncTaskCheckRetryExcDTO extends DTO {
    private BizTaskConfDO bizTaskConfDO;
    private String errorCode;

    private Long bizTaskId;
    private String customerId;

    private String script;
    private Map<String, Object> variables;
}
