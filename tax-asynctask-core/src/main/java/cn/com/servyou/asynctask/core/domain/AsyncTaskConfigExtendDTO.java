package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/11 18:31
 */
@Slf4j
@NoArgsConstructor
@Data
public class AsyncTaskConfigExtendDTO extends DTO {

    /**
     * 是否需要通知结果
     */
    private Boolean isNeedNotify;
    /**
     * 通知接口
     */
    private String notifyInterface;
    /**
     * 通知方法
     */
    private String notifyMethod;
    /**
     * 通知参数
     */
    private String notifyParamType;

    /**
     * 生命周期bean，不为空时，表示是标准模式，bean 必须实现taskLifeCycle接口
     */
    private String lifeBean;

    public static AsyncTaskConfigExtendDTO toDTO(String extendJson) {
        if (StrUtil.isBlank(extendJson)) {
            return null;
        }
        try {
            return JSON.parseObject(extendJson, AsyncTaskConfigExtendDTO.class);
        } catch (Exception e) {
            log.error("json解析异常,json=" + extendJson, e);
            return null;
        }
    }

    public Boolean isNotifyAble(){
        return BooleanUtil.isTrue(isNeedNotify) && StrUtil.isNotBlank(notifyInterface) && StrUtil.isNotBlank(notifyMethod) && StrUtil.isNotBlank(notifyParamType);
    }

}
