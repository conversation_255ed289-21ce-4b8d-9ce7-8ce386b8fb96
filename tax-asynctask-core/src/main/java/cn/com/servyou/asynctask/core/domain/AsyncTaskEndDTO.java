package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.asynctask.core.enums.TaskEndEnum;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 11:21
 */
@Data
public class AsyncTaskEndDTO extends DTO {
    /**
     * 取消来源
     *
     * @see TaskEndEnum
     */
    private TaskEndEnum endSource;

    /**
     * 被取消的任务ID
     */
    private Long bizTaskId;

    /**
     * 被取消任务的customerId
     */
    private String customerId;
    /**
     * 合并后的任务ID
     */
    private Long mergeBizTaskId;

    /**
     * 状态
     *
     * @see cn.com.servyou.asynctask.core.enums.TaskStatusEnum
     */
    private Integer status;

    /**
     * 执行次数达到上限，只修改end_flag,不修改状态，保留最后一次执行的状态。
     *
     * @param bizTaskId
     * @param customerId
     * @return
     */
    public static AsyncTaskEndDTO endForExecLimit(Long bizTaskId, String customerId) {
        AsyncTaskEndDTO asyncTaskEndDTO = new AsyncTaskEndDTO();
        asyncTaskEndDTO.setCustomerId(customerId);
        asyncTaskEndDTO.setBizTaskId(bizTaskId);
        asyncTaskEndDTO.setEndSource(TaskEndEnum.EXEC_LIMIT);
        return asyncTaskEndDTO;
    }

    /**
     *  合并任务，修改end_flag,且修改状态为任务取消
     * @param bizTaskId
     * @param customerId
     * @param mergeBizTaskId
     * @return
     */
    public static AsyncTaskEndDTO endForMerge(Long bizTaskId, String customerId, Long mergeBizTaskId) {
        AsyncTaskEndDTO asyncTaskEndDTO = new AsyncTaskEndDTO();
        asyncTaskEndDTO.setCustomerId(customerId);
        asyncTaskEndDTO.setBizTaskId(bizTaskId);
        asyncTaskEndDTO.setMergeBizTaskId(mergeBizTaskId);
        asyncTaskEndDTO.setStatus(TaskStatusEnum.CANCEL.getCode());
        asyncTaskEndDTO.setEndSource(TaskEndEnum.MERGED);
        return asyncTaskEndDTO;
    }


}
