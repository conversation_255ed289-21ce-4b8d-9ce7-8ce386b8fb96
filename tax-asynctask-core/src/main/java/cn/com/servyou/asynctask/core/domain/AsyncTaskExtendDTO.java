package cn.com.servyou.asynctask.core.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description Too late to explain
 * biz_task.extend字段格式定义
 * <AUTHOR>
 * @Date 2025/3/13 10:00
 */
@Data
public class AsyncTaskExtendDTO implements Serializable {
    private static final long serialVersionUID = 3488324627711222924L;
    /**
     * 申请排队的透传参数，数据来源，创建任务业务方传入。
     */
    private String extData;

    /**
     * 未排队已执行次数
     */
    private Integer unQueueExecutedNum;

    /**
     * dubbo上下文排队信息
     */
    private String ctxBizPartCode;
    private String ctxBizCode;
    private String ctxTicket;
    /**
     * 17boot上下文排队信息
     */
    private String xCtxBizPartCode;
    private String xCtxBizCode;
    private String xCtxTicket;
}
