package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/15 17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTaskMergeDTO extends DTO {

    /**
     * 是否执行任务
     */
    private Boolean isExecTask;
    /**
     * 被合并取消的任务id
     */
    private Long cancelBizTaskId;

    /**
     * 可的任务id
     */
    private Long execbizTaskId;
}
