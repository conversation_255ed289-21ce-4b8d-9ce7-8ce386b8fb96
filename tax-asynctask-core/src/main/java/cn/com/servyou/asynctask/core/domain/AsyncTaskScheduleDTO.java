package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 13:37
 */
@Data
public class AsyncTaskScheduleDTO extends DTO {

    /**
     * 需要调度的任务
     */
    private BizTaskDO bizTaskDO;

    /**
     * 是否是未排队异常
     */
    private Boolean isUnQueuedExc;

    /**
     * 是否需要通知... 有点搓
     */
    private Boolean needNotifyResult = Boolean.TRUE;

    /**
     * 是否是排队阶段异常，还没触发到业务
     */
    private Boolean lineupAPIError = Boolean.FALSE;

    private String errorCode;
    private String errorMessage;


    public AsyncTaskScheduleDTO() {
    }

    public AsyncTaskScheduleDTO(BizTaskDO bizTaskDO, Boolean isUnQueuedExc) {
        this.bizTaskDO = bizTaskDO;
        this.isUnQueuedExc = isUnQueuedExc;
    }
}
