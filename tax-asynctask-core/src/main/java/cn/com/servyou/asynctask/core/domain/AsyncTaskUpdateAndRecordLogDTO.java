package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.asynctask.core.enums.EndFlagEnum;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.dataobj.BizTaskLogDO;
import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

import java.util.Date;
import java.util.Optional;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/19 20:02
 */
@Data
public class AsyncTaskUpdateAndRecordLogDTO extends DTO {

    /**
     * 任务ID
     */
    private Long bizTaskId;
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 任务状态
     */
    private TaskStatusEnum taskStatus;
    /**
     * 结束标识
     */
    private EndFlagEnum endFlag;
    /**
     * 失败原因
     */
    private String reason;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    private String bizPartCode;
    private String bizCode;


    public BizTaskDO buildBizTaskDO() {
        BizTaskDO bizTaskDO = new BizTaskDO();
        bizTaskDO.setId(this.bizTaskId);
        bizTaskDO.setCustomerId(this.customerId);
        bizTaskDO.setStatus(Optional.ofNullable(this.taskStatus).map(TaskStatusEnum::getCode).orElse(null));
        bizTaskDO.setEndFlag(Optional.ofNullable(this.endFlag).map(EndFlagEnum::getCode).orElse(null));
        bizTaskDO.setReason(this.reason);
        return bizTaskDO;
    }
    public BizTaskLogDO buildBizTaskLogDO() {
        BizTaskLogDO bizTaskLogDO = new BizTaskLogDO();
        bizTaskLogDO.setTaskId(this.bizTaskId);
        bizTaskLogDO.setStartTime(this.startTime);
        bizTaskLogDO.setEndTime(this.endTime);
        bizTaskLogDO.setReason(this.reason);
        bizTaskLogDO.setStatus(Optional.ofNullable(this.taskStatus).map(TaskStatusEnum::getCode).orElse(null));
        return bizTaskLogDO;
    }


}
