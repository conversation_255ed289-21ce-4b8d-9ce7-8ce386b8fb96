package cn.com.servyou.asynctask.core.domain;

import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 16:58
 */
@Data
public class AsyncTaskUpdateDTO extends DTO {

    private Long bizTaskId;
    private String customerId;
    private Integer originExecuteNum;

    public static AsyncTaskUpdateDTO buildForScheduleTask(Long bizTaskId, String customerId,
                                                          Integer originExecuteNum) {
        AsyncTaskUpdateDTO asyncTaskUpdateDTO = new AsyncTaskUpdateDTO();
        asyncTaskUpdateDTO.setBizTaskId(bizTaskId);
        asyncTaskUpdateDTO.setCustomerId(customerId);
        asyncTaskUpdateDTO.setOriginExecuteNum(originExecuteNum);
        return asyncTaskUpdateDTO;
    }
}
