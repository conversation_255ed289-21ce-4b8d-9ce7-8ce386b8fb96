package cn.com.servyou.asynctask.core.enums;

import cn.com.servyou.xqy.framework.core.BusinessMessage;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description 异常码
 * <AUTHOR>
 * @Date 2023/12/12 11:30
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeEnum implements BusinessMessage {

    COMMON_PARAM_NOT_EMPTY("000001", "参数对象不能为空"),
    ACCOUNT_ID_NOT_EMPTY("000002", "用户id不能为空"),
    COMPANY_ID_NOT_EMPTY("000003","企业id不能为空"),
    TAX_NO_NOT_EMPTY("000004","企业税号不能为空"),
    AREA_CODE_NOT_EMPTY("000005","地区编码不能为空"),
    BIZ_PART_CODE_NOT_EMPTY("000006","业务方代码不能为空"),
    BIZ_CODE_NOT_EMPTY("000007","业务代码不能为空"),
    TICKET_NOT_EMPTY("000008","ticket不能为空"),
    TASK_CONF_EMPTY("TASK_CONF_EMPTY", "业务未配置"),
    CREATE_TASK_ERROR("CREATE_TASK_ERROR", "创建任务异常"),
    UPDATE_TASK_ERROR("UPDATE_TASK_ERROR", "更新任务异常"),
    ADD_TASK_LOG("ADD_TASK_LOG", "记录任务日志异常"),
    INVALID_PARAM_ERROR("111111", "%s"),


    ;

    private final String code;

    private final String message;
}
