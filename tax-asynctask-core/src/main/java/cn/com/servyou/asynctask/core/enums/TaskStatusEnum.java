package cn.com.servyou.asynctask.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/18 23:09
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    /**
     * 未执行
     */
    NOT_EXECUTE(0, "未执行"),

    /**
     * 执行中
     */
    EXECUTING(1, "执行中"),

    /**
     * 执行成功
     */
    SUCCESS(2, "执行成功"),

    /**
     * 执行失败
     */
    FAIL(3, "执行失败"),

    /**
     * 执行终止
     */
    CANCEL(4, "任务取消");

    private Integer code;
    private String name;
}
