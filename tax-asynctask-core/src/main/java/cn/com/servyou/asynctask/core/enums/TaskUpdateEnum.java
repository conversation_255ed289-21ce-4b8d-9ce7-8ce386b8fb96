package cn.com.servyou.asynctask.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 16:59
 */
@Getter
@AllArgsConstructor
public enum TaskUpdateEnum {

    /**
     * update biz_task set execute_num = ? where id = ? and customer_id = ? and execute_num = ?
     */
    SCHEDULE("任务调度"),

    ;
    private String desc;
}
