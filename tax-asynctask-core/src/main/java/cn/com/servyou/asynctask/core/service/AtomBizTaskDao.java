package cn.com.servyou.asynctask.core.service;

import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/8/15 10:19
 */
public interface AtomBizTaskDao {
    BizTaskDO selectByPrimaryKey(BizTaskDO record);

    int updateTaskEndStatusById(BizTaskDO record);
    int updateTaskEndStatusByCode(BizTaskDO record);

    int scheduleTask(BizTaskDO record);

    int createTask(BizTaskDO bizTaskDO);

    int updateTaskStatus(BizTaskDO record);
    int updateTaskTicket(BizTaskDO record);
    int updateTaskExtend(BizTaskDO record);
}
