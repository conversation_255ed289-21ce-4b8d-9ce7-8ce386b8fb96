package cn.com.servyou.asynctask.core.service;

import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/15 20:42
 */
public interface BizTaskConfService {
    /**
     * 根据bizPartCode,bizCode查询配置
     *
     * @param bizTaskConfDO
     * @return
     */
    BizTaskConfDO getBizTaskConfByPartAndBizCode(BizTaskConfDO bizTaskConfDO);

    BizTaskConfDO getBizTaskConfByTaskCode(BizTaskConfDO bizTaskConfDO);

    /**
     * 内部测试用，新增||更新配置
     * @param bizTaskConfDO
     * @return
     */
    Boolean updateBizTaskConf(BizTaskConfDO bizTaskConfDO);
}
