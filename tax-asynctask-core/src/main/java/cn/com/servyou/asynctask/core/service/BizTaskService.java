package cn.com.servyou.asynctask.core.service;

import cn.com.servyou.asynctask.core.domain.AsyncTaskBaseDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskCancelDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskEndDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskUpdateDTO;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 11:19
 */
public interface BizTaskService {
    Boolean createTask(BizTaskDO bizTaskDO);

    /**
     * 任务被结束，内部调用
     *
     * @param asyncTaskEndDTO
     */
    void endTask(AsyncTaskEndDTO asyncTaskEndDTO);

    /**
     * 取消任务-对外facade用
     *
     * @param asyncTaskCancelDTO
     */
    void cancelTask(AsyncTaskCancelDTO asyncTaskCancelDTO);

    /**
     * 更新任务
     *
     * @param asyncTaskUpdateDTO
     * @return
     */

    Boolean scheduleTask(AsyncTaskUpdateDTO asyncTaskUpdateDTO);

    /**
     * 根据分区字段+主键查询
     *
     * @param asyncTaskBaseDTO
     * @return
     */

    BizTaskDO getTaskById(AsyncTaskBaseDTO asyncTaskBaseDTO);

    Boolean updateTaskStatus(BizTaskDO bizTaskDO);

    Boolean updateTaskTicket(BizTaskDO bizTaskDO);

    Boolean updateTaskExtend(BizTaskDO bizTaskDO);
}
