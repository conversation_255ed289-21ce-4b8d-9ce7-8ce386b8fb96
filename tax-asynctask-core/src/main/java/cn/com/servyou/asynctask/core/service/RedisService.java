package cn.com.servyou.asynctask.core.service;

import java.util.concurrent.TimeUnit;

public interface RedisService {

    void hput(String cacheKey, String hashKey, Object value);

    String hget(String cacheKey, String hashKey);

    void hdelByHashKey(String cacheKey, String... hashKey);

    void delByCacheKey(String cacheKey);
    void delByCacheKeyIfValuePresent(String cacheKey, String value);

    String get(String cacheKey);
    void set(String cacheKey, String value, long timeout, TimeUnit timeUnit);

    void setIfPresent(String cacheKey, String value);
}
