package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.service.AtomBizTaskDao;
import cn.com.servyou.asynctask.dao.BizTaskDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/8/15 10:19
 */
@Slf4j
@Service
public class AtomBizTaskDaoImpl implements AtomBizTaskDao {
    @Autowired
    private BizTaskDAO bizTaskDAO;

    @Override
    public BizTaskDO selectByPrimaryKey(BizTaskDO record) {
        return bizTaskDAO.selectByPrimaryKey(record.getId(), record.getCustomerId());
    }

    @Override
    public int updateTaskEndStatusById(BizTaskDO record) {
        return bizTaskDAO.updateTaskEndStatusById(record);
    }

    @Override
    public int updateTaskEndStatusByCode(BizTaskDO record) {
        return bizTaskDAO.updateTaskEndStatusByCode(record);
    }

    @Override
    public int scheduleTask(BizTaskDO record) {
        return bizTaskDAO.scheduleTask(record);
    }

    @Override
    public int createTask(BizTaskDO bizTaskDO) {
        return bizTaskDAO.createTask(bizTaskDO);
    }

    @Override
    public int updateTaskStatus(BizTaskDO record) {
        return bizTaskDAO.updateTaskStatus(record);
    }

    @Override
    public int updateTaskTicket(BizTaskDO record) {
        return bizTaskDAO.updateTaskTicket(record);
    }

    @Override
    public int updateTaskExtend(BizTaskDO record) {
        return bizTaskDAO.updateTaskExtend(record);
    }
}
