package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.config.TaskConfig;
import cn.com.servyou.asynctask.core.service.AtomBizTaskDao;
import cn.com.servyou.asynctask.dao.BizTaskInfoDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Supplier;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/8/15 10:20
 */
@Slf4j
@Service
public class AtomBizTaskInfoDaoImpl implements AtomBizTaskDao {
    @Autowired
    private BizTaskInfoDAO bizTaskInfoDAO;
    @Autowired
    private TaskConfig taskConfig;

    @Override
    public BizTaskDO selectByPrimaryKey(BizTaskDO record) {
        return processSelect(record, () -> bizTaskInfoDAO.selectByPrimaryKey(record));
    }

    @Override
    public int updateTaskEndStatusById(BizTaskDO record) {
        return processUpdate(record, () -> bizTaskInfoDAO.updateTaskEndStatusById(record));
    }

    @Override
    public int updateTaskEndStatusByCode(BizTaskDO record) {
        return processUpdate(record, () -> bizTaskInfoDAO.updateTaskEndStatusByCode(record));
    }

    @Override
    public int scheduleTask(BizTaskDO record) {
        return processUpdate(record, () -> bizTaskInfoDAO.scheduleTask(record));
    }

    @Override
    public int createTask(BizTaskDO bizTaskDO) {
        return bizTaskInfoDAO.createTask(bizTaskDO);
    }

    @Override
    public int updateTaskStatus(BizTaskDO record) {
        return processUpdate(record, () -> bizTaskInfoDAO.updateTaskStatus(record));
    }

    @Override
    public int updateTaskTicket(BizTaskDO record) {
        return processUpdate(record, () -> bizTaskInfoDAO.updateTaskTicket(record));
    }

    @Override
    public int updateTaskExtend(BizTaskDO record) {
        return processUpdate(record, () -> bizTaskInfoDAO.updateTaskExtend(record));
    }



    private int processUpdate(BizTaskDO record, Supplier<Integer> updateOperation) {
        record.setApplyDate(DateUtil.date());
        int result = updateOperation.get();

        if (result == 0 && taskConfig.isCriticalTime()) {
            record.setApplyDate(DateUtil.yesterday());
            result = updateOperation.get();
        }

        return result;
    }

    private BizTaskDO processSelect(BizTaskDO record, Supplier<BizTaskDO> selectOperation) {
        record.setApplyDate(DateUtil.date());
        BizTaskDO result = selectOperation.get();

        if (result == null && taskConfig.isCriticalTime()) {
            record.setApplyDate(DateUtil.yesterday());
            result = selectOperation.get();
        }

        return result;
    }
}
