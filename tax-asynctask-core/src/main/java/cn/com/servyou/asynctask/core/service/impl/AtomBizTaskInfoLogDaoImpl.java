package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.service.AtomBizTaskLogDao;
import cn.com.servyou.asynctask.dao.BizTaskInfoLogDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskLogDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/8/15 09:54
 */
@Slf4j
@Service
public class AtomBizTaskInfoLogDaoImpl implements AtomBizTaskLogDao {

    @Autowired
    private BizTaskInfoLogDAO bizTaskInfoLogDAO;
    @Override
    public int recordLog(BizTaskLogDO bizTaskLogDO) {
        return bizTaskInfoLogDAO.recordLog(bizTaskLogDO);
    }
}
