package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.service.AtomBizTaskLogDao;
import cn.com.servyou.asynctask.dao.BizTaskLogDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskLogDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/8/15 09:53
 */
@Slf4j
@Service
public class AtomBizTaskLogDaoImpl implements AtomBizTaskLogDao {
    @Autowired
    private BizTaskLogDAO bizTaskLogDAO;

    @Override
    public int recordLog(BizTaskLogDO bizTaskLogDO) {
        return bizTaskLogDAO.recordLog(bizTaskLogDO);
    }
}
