package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.common.RedisServiceTemplate;
import cn.com.servyou.asynctask.core.service.BizTaskConfService;
import cn.com.servyou.asynctask.dao.BizTaskConfDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/15 20:42
 */
@Slf4j
@Service
public class BizTaskConfServiceImpl implements BizTaskConfService {
    @Autowired
    private BizTaskConfDAO bizTaskConfDAO;
    @Autowired
    private RedisServiceTemplate redisServiceTemplate;

    @Override
    public BizTaskConfDO getBizTaskConfByPartAndBizCode(BizTaskConfDO bizTaskConfDO) {
        if (null == bizTaskConfDO || StrUtil.isEmpty(bizTaskConfDO.getBizPartCode()) || StrUtil.isEmpty(bizTaskConfDO.getBizCode())) {
            log.info("bizTaskConfDO || bizPartCode || bizCode is null, {}", JSONUtil.toJsonStr(bizTaskConfDO));
            return null;
        }
        return redisServiceTemplate.executeSingle(bizTaskConfDO.getBizPartCode(),
                "getBizTaskConfByPartAndBizCode" + bizTaskConfDO.getBizCode(), BizTaskConfDO.class,
                () -> bizTaskConfDAO.selectByBizPartCodeAndBizCode(bizTaskConfDO.getBizPartCode(),
                        bizTaskConfDO.getBizCode()));

    }

    @Override
    public BizTaskConfDO getBizTaskConfByTaskCode(BizTaskConfDO bizTaskConfDO) {
        if (null == bizTaskConfDO || StrUtil.isEmpty(bizTaskConfDO.getTaskCode())) {
            log.info("bizTaskConfDO || taskCode is null, {}", JSONUtil.toJsonStr(bizTaskConfDO));
            return null;
        }
        return redisServiceTemplate.executeSingle(bizTaskConfDO.getTaskCode(),
                "getBizTaskConfByTaskCode", BizTaskConfDO.class,
                () -> bizTaskConfDAO.selectByTaskCode(bizTaskConfDO.getTaskCode()));
    }

    @Override
    public Boolean updateBizTaskConf(BizTaskConfDO bizTaskConfDO) {
        if (null == bizTaskConfDO.getId()) {
            return this.bizTaskConfDAO.insertSelective(bizTaskConfDO) > 0;
        } else {
            return this.bizTaskConfDAO.updateByPrimaryKeySelective(bizTaskConfDO) > 0;
        }
    }
}
