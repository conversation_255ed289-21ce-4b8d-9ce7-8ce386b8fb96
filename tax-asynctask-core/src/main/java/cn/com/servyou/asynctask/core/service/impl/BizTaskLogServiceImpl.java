package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.config.TaskConfig;
import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.asynctask.core.enums.RouteModeEnum;
import cn.com.servyou.asynctask.core.service.AtomBizTaskLogDao;
import cn.com.servyou.asynctask.core.service.BizTaskLogService;
import cn.com.servyou.asynctask.core.utils.AssertUtil;
import cn.com.servyou.asynctask.dataobj.BizTaskLogDO;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/19 19:55
 */
@Slf4j
@Service
public class BizTaskLogServiceImpl implements BizTaskLogService {

    @Autowired
    private AtomBizTaskLogDaoImpl atomBizTaskLogDaoImpl;
    @Autowired
    private AtomBizTaskInfoLogDaoImpl atomBizTaskInfoLogDaoImpl;
    @Autowired
    private TaskConfig taskConfig;

    /**
     * 决策新老表DAO
     * @return
     */
    public AtomBizTaskLogDao atomBizTaskLogDao() {
        if (StrUtil.equals(taskConfig.getRouteConfig().getCurrentMode(), RouteModeEnum.NEW.getCode())) {
            return atomBizTaskInfoLogDaoImpl;
        } else {
            // 默认返回普通的日志服务
            return atomBizTaskLogDaoImpl;
        }
    }

    @Override
    public Boolean recordLog(BizTaskLogDO bizTaskLogDO) {
        AssertUtil.notNull(bizTaskLogDO, ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "参数为空");
        AssertUtil.notNull(bizTaskLogDO.getTaskId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "任务ID为空");
        AssertUtil.notNull(bizTaskLogDO.getStartTime(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "开始时间为空");
        AssertUtil.notNull(bizTaskLogDO.getEndTime(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "结束时间为空");
        AssertUtil.notNull(bizTaskLogDO.getStatus(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "状态为空");
        if (StrUtil.length(bizTaskLogDO.getReason()) > 1024) {
            bizTaskLogDO.setReason(StrUtil.sub(bizTaskLogDO.getReason(), 0, 1020));
        }
        int result = atomBizTaskLogDao().recordLog(bizTaskLogDO);
        return result > 0;
    }
}
