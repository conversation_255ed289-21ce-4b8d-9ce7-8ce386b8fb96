package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.config.TaskConfig;
import cn.com.servyou.asynctask.core.domain.AsyncTaskBaseDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskCancelDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskEndDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskUpdateDTO;
import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.asynctask.core.enums.RouteModeEnum;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.asynctask.core.service.AtomBizTaskDao;
import cn.com.servyou.asynctask.core.service.BizTaskService;
import cn.com.servyou.asynctask.core.utils.AssertUtil;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 11:20
 */
@Slf4j
@Service
public class BizTaskServiceImpl implements BizTaskService {

    @Autowired
    private AtomBizTaskDaoImpl atomBizTaskDaoImpl;
    @Autowired
    private AtomBizTaskInfoDaoImpl atomBizTaskInfoDaoImpl;
    @Autowired
    private TaskConfig taskConfig;


    @Override
    public Boolean createTask(BizTaskDO bizTaskDO) {
        bizTaskDO.setEnvironmentCode(taskConfig.getEnvironmentCode());
        int result = atomBizTaskDao().createTask(bizTaskDO);
        return result > 0;
    }

    @Override
    public void endTask(AsyncTaskEndDTO asyncTaskEndDTO) {
        log.info("[任务结束endTask]asyncTaskEndDTO={}", JSONUtil.toJsonStr(asyncTaskEndDTO));
        BizTaskDO record = new BizTaskDO();
        record.setId(asyncTaskEndDTO.getBizTaskId());
        record.setCustomerId(asyncTaskEndDTO.getCustomerId());
        record.setStatus(asyncTaskEndDTO.getStatus());
        record.setMergeBizTaskId(asyncTaskEndDTO.getMergeBizTaskId());
        int result = executeUpdateWithFailover(record, AtomBizTaskDao::updateTaskEndStatusById);
        log.info("[任务结束]任务{}结束-{}，影响行数：{}", asyncTaskEndDTO.getBizTaskId(), asyncTaskEndDTO.getEndSource().getDesc(),
                result);
    }

    /**
     * bizTaskId 有值，按任务ID执行
     * 否则按customerId，areaCode，bizPartCode，bizCode取消任务。若有参数为空，则抛出参数为空异常。
     * 任务取消时，若endFlad=1,则不执行，且无异常抛出。
     *
     * @param asyncTaskCancelDTO
     */
    @Override
    public void cancelTask(AsyncTaskCancelDTO asyncTaskCancelDTO) {
        log.info("[任务取消cancelTask]asyncTaskEndDTO={}", JSONUtil.toJsonStr(asyncTaskCancelDTO));
        AssertUtil.notNull(asyncTaskCancelDTO, ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "参数为空");
        AssertUtil.notEmpty(asyncTaskCancelDTO.getCustomerId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "客户ID为空");
        BizTaskDO record = new BizTaskDO();
        record.setId(asyncTaskCancelDTO.getBizTaskId());
        record.setCustomerId(asyncTaskCancelDTO.getCustomerId());
        record.setStatus(TaskStatusEnum.CANCEL.getCode());
        if (null != asyncTaskCancelDTO.getBizTaskId()) {
            int result = executeUpdateWithFailover(record, AtomBizTaskDao::updateTaskEndStatusById);
            log.info("[任务取消]通过任务ID{}取消任务，影响行数：{}", record.getId(), result);
            return;
        }
        AssertUtil.notEmpty(asyncTaskCancelDTO.getAreaCode(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "地区代码为空");
        AssertUtil.notEmpty(asyncTaskCancelDTO.getBizPartCode(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(),
                "业务方代码为空");
        AssertUtil.notEmpty(asyncTaskCancelDTO.getBizCode(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "业务代码为空");
        record.setAreaCode(asyncTaskCancelDTO.getAreaCode());
        record.setBizPartCode(asyncTaskCancelDTO.getBizPartCode());
        record.setBizCode(asyncTaskCancelDTO.getBizCode());
        record.setEnvironmentCode(taskConfig.getEnvironmentCode());
        int result = executeUpdateWithFailover(record, AtomBizTaskDao::updateTaskEndStatusByCode);
        log.info("[任务取消]通过业务代码{}取消任务，影响行数：{}", JSONUtil.toJsonStr(record), result);
    }

    @Override
    public Boolean scheduleTask(AsyncTaskUpdateDTO asyncTaskUpdateDTO) {
        BizTaskDO bizTaskDO = new BizTaskDO();
        bizTaskDO.setId(asyncTaskUpdateDTO.getBizTaskId());
        bizTaskDO.setCustomerId(asyncTaskUpdateDTO.getCustomerId());
        bizTaskDO.setExecuteNum(asyncTaskUpdateDTO.getOriginExecuteNum());
        bizTaskDO.setStatus(TaskStatusEnum.EXECUTING.getCode());
        int result = executeUpdateWithFailover(bizTaskDO, AtomBizTaskDao::scheduleTask);

        return result > 0;
    }

    @Override
    public BizTaskDO getTaskById(AsyncTaskBaseDTO asyncTaskBaseDTO) {
        AssertUtil.notNull(asyncTaskBaseDTO, ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "参数为空");
        AssertUtil.notEmpty(asyncTaskBaseDTO.getCustomerId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "客户ID为空");
        AssertUtil.notNull(asyncTaskBaseDTO.getBizTaskId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "任务ID为空");
        BizTaskDO record = new BizTaskDO();
        record.setId(asyncTaskBaseDTO.getBizTaskId());
        record.setCustomerId(asyncTaskBaseDTO.getCustomerId());
        return executeSelectWithFailover(record, AtomBizTaskDao::selectByPrimaryKey);
    }

    @Override
    public Boolean updateTaskStatus(BizTaskDO bizTaskDO) {
        AssertUtil.notNull(bizTaskDO, ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "参数为空");
        AssertUtil.notNull(bizTaskDO.getId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "任务ID为空");
        AssertUtil.notEmpty(bizTaskDO.getCustomerId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "客户ID为空");
        if (null == bizTaskDO.getStatus() && StrUtil.isEmpty(bizTaskDO.getEndFlag()) && StrUtil.isEmpty(bizTaskDO.getReason())) {
            log.info("更新字段都为空，不做更新");
            return true;
        }
        if (StrUtil.length(bizTaskDO.getReason()) > 1024) {
            bizTaskDO.setReason(StrUtil.sub(bizTaskDO.getReason(), 0, 1000));
        }
        int result = executeUpdateWithFailover(bizTaskDO, AtomBizTaskDao::updateTaskStatus);

        return result > 0;
    }

    @Override
    public Boolean updateTaskTicket(BizTaskDO bizTaskDO) {
        AssertUtil.notNull(bizTaskDO, ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "参数为空");
        AssertUtil.notNull(bizTaskDO.getId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "任务ID为空");
        AssertUtil.notEmpty(bizTaskDO.getCustomerId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "客户ID为空");
        AssertUtil.notEmpty(bizTaskDO.getLineupTicket(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "Ticket为空");
        int result = executeUpdateWithFailover(bizTaskDO, AtomBizTaskDao::updateTaskTicket);
        return result > 0;
    }

    @Override
    public Boolean updateTaskExtend(BizTaskDO bizTaskDO) {
        AssertUtil.notNull(bizTaskDO, ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "参数为空");
        AssertUtil.notNull(bizTaskDO.getId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "任务ID为空");
        AssertUtil.notEmpty(bizTaskDO.getCustomerId(), ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "客户ID为空");
        int result = executeUpdateWithFailover(bizTaskDO, AtomBizTaskDao::updateTaskExtend);
        return result > 0;
    }





    /**
     * 决策新老表DAO
     * @return
     */
    public AtomBizTaskDao atomBizTaskDao() {
        if (StrUtil.equals(taskConfig.getRouteConfig().getCurrentMode(), RouteModeEnum.NEW.getCode())) {
            return atomBizTaskInfoDaoImpl;
        } else {
            // 默认返回普通的日志服务
            return atomBizTaskDaoImpl;
        }
    }

    public AtomBizTaskDao atomBizTaskDaoCritical() {
        if (!StrUtil.equals(taskConfig.getRouteConfig().getCurrentMode(), RouteModeEnum.NEW.getCode())) {
            return atomBizTaskInfoDaoImpl;
        } else {
            // 默认返回普通的日志服务
            return atomBizTaskDaoImpl;
        }
    }

    @FunctionalInterface
    private interface UpdateOperation<T> {
        int execute(AtomBizTaskDao dao, T record);
    }
    @FunctionalInterface
    private interface SelectOperation<T> {
        BizTaskDO execute(AtomBizTaskDao dao, T record);
    }
    private <T> int executeUpdateWithFailover(T record, UpdateOperation<T> operation) {
        int result = operation.execute(atomBizTaskDao(), record);
        if (result == 0 && taskConfig.isRouteCritical()) {
            result = operation.execute(atomBizTaskDaoCritical(), record);
        }
        return result;
    }

    private <T> BizTaskDO executeSelectWithFailover(T record, SelectOperation<T> operation) {
        BizTaskDO result = operation.execute(atomBizTaskDao(), record);
        if (result == null && taskConfig.isRouteCritical()) {
            result = operation.execute(atomBizTaskDaoCritical(), record);
        }
        return result;
    }
}
