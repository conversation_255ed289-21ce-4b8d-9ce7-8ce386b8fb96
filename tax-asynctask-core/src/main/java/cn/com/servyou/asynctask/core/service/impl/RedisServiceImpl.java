package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.service.RedisService;
import cn.com.servyou.i7boot.cache.RedisInject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class RedisServiceImpl implements RedisService {

    @RedisInject
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void hput(String cacheKey, String hashKey, Object value) {
        this.redisTemplate.opsForHash().put(cacheKey, hashKey, JSONUtil.toJsonStr(value));
    }

    @Override
    public String hget(String cacheKey, String hashKey) {
        String value = (String) this.redisTemplate.opsForHash().get(cacheKey, hashKey);
        return value;
    }

    @Override
    public void hdelByHashKey(String cacheKey, String... hashKey) {
        Long count = this.redisTemplate.opsForHash().delete(cacheKey, hashKey);
        log.info("hdelByHashKey({},{})删除了{}个", cacheKey, JSON.toJSONString(hashKey), count);
    }

    @Override
    public void delByCacheKey(String cacheKey) {
        Boolean res = this.redisTemplate.delete(cacheKey);
        log.info("hdelByCacheKey({})删除{}", cacheKey, res);
    }

    @Override
    public void delByCacheKeyIfValuePresent(String cacheKey, String value) {
        String cacheVal = get(cacheKey);
        if (value.equals(cacheVal)) {
            delByCacheKey(cacheKey);
        }
    }

    @Override
    public String get(String cacheKey) {
        return (String) this.redisTemplate.opsForValue().get(cacheKey);
    }

    @Override
    public void set(String cacheKey, String value, long timeout, TimeUnit timeUnit) {
        this.redisTemplate.opsForValue().set(cacheKey, value, timeout, timeUnit);
    }

    @Override
    public void setIfPresent(String cacheKey, String value) {
        Long expire = this.redisTemplate.getExpire(cacheKey, TimeUnit.SECONDS);
        this.redisTemplate.opsForValue().set(cacheKey, value, expire, TimeUnit.SECONDS);
    }

}
