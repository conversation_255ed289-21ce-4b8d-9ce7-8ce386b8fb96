//package cn.com.servyou.asynctask.core.service.impl;
//
//import cn.com.servyou.asynctask.core.service.TaskInvokeService;
//import cn.hutool.extra.spring.SpringUtil;
//import com.alibaba.dubbo.rpc.Result;
//import com.alibaba.dubbo.rpc.RpcContext;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.lang.reflect.Method;
//import java.util.concurrent.CompletableFuture;
//
//
//@Service
//public class TaskInvokeServiceImpl implements TaskInvokeService {
//
//
////    @Autowired
////    private DeclarationFacade declarationFacade;
//
//    private void invokeInterface() throws NoSuchMethodException {
//        String interfaceName = "TestService";
//        String methodName = "test";
//        Object[] args = {"arg1", "arg2"};
//
//        Object invokeService = SpringUtil.getBean(interfaceName);
//
//        Method method = invokeService.getClass().getMethod(methodName);
//
//
//        CompletableFuture<Object> future = (CompletableFuture<Object>) RpcContext.getContext().asyncCall(() -> method.invoke(invokeService, args));
//
//        future.whenComplete((v, t) -> {
//            if (t != null) {
//                // 处理异常
//            } else {
//                // 处理结果
//            }
//        });
//    }
//
//
//}
