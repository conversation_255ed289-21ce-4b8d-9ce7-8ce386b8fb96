package cn.com.servyou.asynctask.core.service.impl;

import cn.com.servyou.asynctask.core.common.RedisServiceTemplate;
import cn.com.servyou.asynctask.core.convert.TestCoreConvert;
import cn.com.servyou.asynctask.core.domain.Test;
import cn.com.servyou.asynctask.core.service.TestService;
import cn.com.servyou.asynctask.dao.BizTaskConfDAO;
import cn.com.servyou.asynctask.dao.TestDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.i7boot.cache.RedisInject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class TestServiceImpl implements TestService {

    @Autowired
    private TestDAO testDAO;
    @Autowired
    private BizTaskConfDAO bizTaskConfDAO;
    @Autowired
    private RedisServiceTemplate redisServiceTemplate;

    @RedisInject
    private RedisTemplate<String, Test> testRedisTemplate;

    @RedisInject
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public List<Test> get() {
        return TestCoreConvert.toList(testDAO.select());
    }

    @Override
    public String testRedis() {
        String stringValue = redisTemplate.opsForValue().get("17bootString");
        if (stringValue == null) {
            redisTemplate.opsForValue().set("17bootString", "Hello world");
        }
        Test testValue = testRedisTemplate.opsForValue().get("17bootTest");
        if (testValue == null) {
            testRedisTemplate.opsForValue().set("17bootTest", new Test());

        }
        return (testValue == null ? "null" : testValue.toString()) + ":" + stringValue;
    }

    @Override
    public BizTaskConfDO getById(Long id) {
        BizTaskConfDO areaConfig = redisServiceTemplate.executeSingle(String.valueOf(id),
                BizTaskConfDO.class, 120, TimeUnit.SECONDS, () -> bizTaskConfDAO.selectByPrimaryKey(id));
        return areaConfig;
    }
}
