package cn.com.servyou.asynctask.core.utils;

import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.xqy.framework.core.BusinessMessage;
import cn.com.servyou.xqy.framework.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 检验参数工具类
 * @since 2024/8/15
 */
public class AssertUtil {

    public static void notNull(Object object, BusinessMessage BusinessMessage) {
        if (object == null) {
            throw new BusinessException(BusinessMessage);
        }
    }

    public static void notEmpty(BusinessMessage BusinessMessage, String... values) {
        for (String value : values) {
            if (StringUtils.isEmpty(value)) {
                throw new BusinessException(BusinessMessage);
            }
        }

    }

    public static void notEmpty(String value, String msg) {
        if (StringUtils.isEmpty(value)) {
            throw new BusinessException(ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), msg);
        }
    }

    public static void notEmpty(String value, String code, String msg) {
        if (StringUtils.isEmpty(value)) {
            throw new BusinessException(code, msg);
        }
    }

    public static void notNull(Object obj, String code, String msg) {
        if (null == obj) {
            throw new BusinessException(code, msg);
        }
    }

    /**
     * true 抛出异常
     */
    public static void isTrue(Boolean whether, String code, String msg) {
        if(whether){
            throw new BusinessException(code, msg);
        }
    }


}
