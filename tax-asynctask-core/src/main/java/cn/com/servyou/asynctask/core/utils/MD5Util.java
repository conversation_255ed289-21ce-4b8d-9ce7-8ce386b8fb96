package cn.com.servyou.asynctask.core.utils;

import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.xqy.framework.exception.BusinessException;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.MD5;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 10:10
 */
@Slf4j
public class MD5Util {

    private static final MD5 md5 = MD5.create();

    public static String calculateMD5(String input) {
        return md5.digestHex(input);
    }

    public static <T> String calculateMD5(T obj, List<String> fields) {
        if (CollUtil.isEmpty(fields)) {
            return null;
        }
        try {
            StringBuilder sb = new StringBuilder();
            // 遍历字段名集合
            for (String fieldName : fields) {
                // 通过反射获取字段
                Field field = obj.getClass().getDeclaredField(fieldName);
                field.setAccessible(true); // 设置为可访问

                // 获取字段值
                Object value = field.get(obj);
                // 将字段值追加到字符串构建器中
                if (value != null) {
                    sb.append(value.toString());
                }
            }
            // 计算MD5
            return calculateMD5(sb.toString());
        } catch (NoSuchFieldException e) {
            log.error("计算MD5错误，字段不存在", e);
        } catch (IllegalAccessException e) {
            log.error("计算MD5错误，字段受保护，取值失败", e);
        } catch (Exception e) {
            log.error("计算MD5错误", e);
        }
        throw new BusinessException(ErrorCodeEnum.INVALID_PARAM_ERROR.getCode(), "计算MD5错误");
    }

}
