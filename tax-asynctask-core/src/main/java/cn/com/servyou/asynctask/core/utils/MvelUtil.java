package cn.com.servyou.asynctask.core.utils;

import org.mvel2.MVEL;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/18 15:17
 */
public class MvelUtil {
    public static void main(String[] args) {
        String script = "!(['LOGIN0001'].contains(errorCode))";


        Map<String, Object> variables = new HashMap<>();
        variables.put("errorCode", "LOGIN0002");

        Serializable s = MVEL.compileExpression(script, variables);
        System.out.println(s);
        Object o = MVEL.executeExpression(s, variables);
        System.out.println(o);
//        try {
////            String script = "!([''LOGIN0001''].contains(errorCode))";
//
//            Map<String, Object> vars = new HashMap<>();
//            vars.put("errorCode", "LOGIN0002");
//            Object result = MVEL.eval(script, vars);
//            System.out.println("result:"+result);
//        } catch (Throwable e) {
//            System.out.println("--------------------------------------------------------------------------------------------------------------------------------");
//            e.printStackTrace();
//        }


    }
}
