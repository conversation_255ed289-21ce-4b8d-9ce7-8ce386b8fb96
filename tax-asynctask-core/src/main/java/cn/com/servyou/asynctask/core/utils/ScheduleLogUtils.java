package cn.com.servyou.asynctask.core.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/20 17:15
 */
public class ScheduleLogUtils {
    private static final Logger LINEUP_LOGGER = LoggerFactory.getLogger("SCHEDULE-DIGEST");

    public static void log(String step, Long bizTaskId, String customerId, String content, Object... args) {
        LINEUP_LOGGER.info(String.format("[%s][%s][%s] %s", step, bizTaskId, customerId, content), args);
    }

}
