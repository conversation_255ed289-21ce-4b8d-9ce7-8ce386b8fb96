<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- maven模型版本 maven2/maven3 使用4.0.0-->
    <modelVersion>4.0.0</modelVersion>
    <!-- 父级项目坐标 (默认从本地查找,未找到再到远程构件仓库查找)-->
    <parent>
        <groupId>cn.com.servyou</groupId>
        <artifactId>tax-asynctask</artifactId>
        <version>${version.number}-${build.number}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <!-- 项目坐标 -->
    <artifactId>tax-asynctask-dao</artifactId>

    <dependencies>
        <!-- framework dependencies -->
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-security</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-mybatis-springboot</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <!-- framework dependencies -->

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
