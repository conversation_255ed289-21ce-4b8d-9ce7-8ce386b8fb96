package cn.com.servyou.asynctask.dao;

import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BizTaskConfDAO {
    int deleteByPrimaryKey(Long id);

    int insert(BizTaskConfDO record);

    int insertSelective(BizTaskConfDO record);

    BizTaskConfDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BizTaskConfDO record);

    int updateByPrimaryKey(BizTaskConfDO record);

    BizTaskConfDO selectByBizPartCodeAndBizCode(@Param("bizPartCode") String bizPartCode, @Param("bizCode") String bizCode);

    BizTaskConfDO selectByTaskCode(@Param("taskCode") String taskCode);

    List<BizTaskConfDO> selectByBizPartCode(@Param("bizPartCode") String bizPartCode);

    List<BizTaskConfDO> findAll();
}