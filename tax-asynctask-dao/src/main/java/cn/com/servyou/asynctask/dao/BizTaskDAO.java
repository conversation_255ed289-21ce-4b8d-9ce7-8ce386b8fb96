package cn.com.servyou.asynctask.dao;

import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface BizTaskDAO {
    int deleteByPrimaryKey(Long id);

    int insert(BizTaskDO record);

    int insertSelective(BizTaskDO record);

    BizTaskDO selectByPrimaryKey(@Param("id") Long id, @Param("customerId") String customerId);

    int updateByPrimaryKeySelective(BizTaskDO record);

    int updateByPrimaryKey(BizTaskDO record);

    int updateTaskEndStatusById(BizTaskDO record);
    int updateTaskEndStatusByCode(BizTaskDO record);

    int scheduleTask(BizTaskDO record);

    int createTask(BizTaskDO bizTaskDO);

    int updateTaskStatus(BizTaskDO record);
    int updateTaskTicket(BizTaskDO record);
    int updateTaskExtend(BizTaskDO record);
}