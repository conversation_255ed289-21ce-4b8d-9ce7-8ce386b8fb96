package cn.com.servyou.asynctask.dao;

import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public interface BizTaskInfoDAO {

    BizTaskDO selectByPrimaryKey(BizTaskDO record);

    int updateTaskEndStatusById(BizTaskDO record);
    int updateTaskEndStatusByCode(BizTaskDO record);

    int scheduleTask(BizTaskDO record);

    int createTask(BizTaskDO bizTaskDO);

    int updateTaskStatus(BizTaskDO record);
    int updateTaskTicket(BizTaskDO record);
    int updateTaskExtend(BizTaskDO record);
}