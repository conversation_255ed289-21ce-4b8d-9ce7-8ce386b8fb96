package cn.com.servyou.asynctask.dao;

import cn.com.servyou.asynctask.dataobj.BizTaskLogDO;
import org.springframework.stereotype.Repository;

@Repository
public interface BizTaskLogDAO {
    int deleteByPrimaryKey(Long id);

    Long insert(BizTaskLogDO record);

    Long insertSelective(BizTaskLogDO record);

    BizTaskLogDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BizTaskLogDO record);

    int updateByPrimaryKey(BizTaskLogDO record);

    int recordLog(BizTaskLogDO record);
}