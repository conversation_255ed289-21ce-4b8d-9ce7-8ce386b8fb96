package cn.com.servyou.asynctask.dataobj;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * biz_task_conf
 */
@Data
public class BizTaskConfDO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 业务方代码
     */
    private String bizPartCode;

    /**
     * 业务方名称
     */
    private String bizPartName;

    /**
     * 业务代码
     */
    private String bizCode;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 执行规则内容|以秒为单位，逗号分隔：180,300,300,300
     */
    private String executeRuleContent;

    /**
     * 任务执行方式
     */
    private Integer execType;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 方法名称
     */
    private String methodName;
    /**
     * 任务代码
     */
    private String taskCode;

    /**
     * 参数类型
     */
    private String paramType;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 任务合并规则内容|字段1,字段2,字段3
     */
    private String mergeRuleContent;

    /**
     * 合并时间范围
     */
    private String mergeTimeRange;

    /**
     * 重试规则内容|!code1, !code2, !code3
     */
    private String retryRuleContent;

    /**
     * 逻辑删除主键ID
     */
    private Long isDelete;

    /**
     * 录入日期
     */
    private Date createDate;

    /**
     * 修改日期
     */
    private Date modifyDate;

    private static final long serialVersionUID = 1L;


}