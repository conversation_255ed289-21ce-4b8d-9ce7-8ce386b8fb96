package cn.com.servyou.asynctask.dataobj;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * biz_task
 */
@Data
public class BizTaskDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;

    /**
     * 逻辑删除主键ID
     */
    private Long isDelete;

    /**
     * 录入日期
     */
    private Date createDate;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 环境代码|环境代码
     */
    private String environmentCode;

    /**
     * 业务方代码
     */
    private String bizPartCode;

    /**
     * 业务代码
     */
    private String bizCode;

    /**
     * 行政区划数字代码
     */
    private String areaCode;

    /**
     * 客户ID|运营端的id
     */
    private String customerId;

    /**
     * 纳税人识别号
     */
    private String taxNo;

    /**
     * 用户ID|账户中心的ID
     */
    private String accountId;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 参数
     */
    private String parameters;

    /**
     * 参数类型
     */
    private String paramType;

    /**
     * 业务标签值
     */
    private String bizTag;

    /**
     * 任务执行方式|0-预排队；1-预执行
     */
    private Integer execType;

    /**
     * 状态|0-待执行；1-执行中；2-执行成功；3-执行失败；4-任务取消
     */
    private Integer status;

    /**
     * 合并后的bizTaskId
     */
    private Long mergeBizTaskId;

    /**
     * 流程是否结束|任务终态标识(0-未结束;1-已结束)
     */
    private String endFlag;

    /**
     * 原因|失败原因
     */
    private String reason;

    /**
     * 执行次数|当前执行次数
     */
    private Integer executeNum;

    /**
     * 执行规则内容|以秒为单位，逗号分隔：0,0,0,0,0,0
     */
    private String executeRuleContent;

    /**
     * 排队凭证|当前凭证
     */
    private String lineupTicket;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extend;



    private String taskCode;

    /**
     * 申请时间
     */
    private Date applyDate;
}