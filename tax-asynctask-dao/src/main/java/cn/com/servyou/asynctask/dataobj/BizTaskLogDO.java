package cn.com.servyou.asynctask.dataobj;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * biz_task_log
 */
@Data
public class BizTaskLogDO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 逻辑删除主键ID
     */
    private Long isDelete;

    /**
     * 录入日期
     */
    private Date createDate;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 原因|失败原因
     */
    private String reason;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态|2-执行成功；3-执行失败;4-任务取消
     */
    private Integer status;

    /**
     * 申请时间
     */
    private Date applyDate;

    private static final long serialVersionUID = 1L;
}