<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.servyou.asynctask.dao.BizTaskConfDAO">
  <resultMap id="BaseResultMap" type="cn.com.servyou.asynctask.dataobj.BizTaskConfDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_part_code" jdbcType="VARCHAR" property="bizPartCode" />
    <result column="biz_part_name" jdbcType="VARCHAR" property="bizPartName" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="biz_name" jdbcType="VARCHAR" property="bizName" />
    <result column="execute_rule_content" jdbcType="VARCHAR" property="executeRuleContent" />
    <result column="exec_type" jdbcType="INTEGER" property="execType" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode"/>
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="merge_rule_content" jdbcType="VARCHAR" property="mergeRuleContent" />
    <result column="merge_time_range" jdbcType="VARCHAR" property="mergeTimeRange" />
    <result column="retry_rule_content" jdbcType="VARCHAR" property="retryRuleContent" />
    <result column="is_delete" jdbcType="BIGINT" property="isDelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_part_code, biz_part_name, biz_code, biz_name, execute_rule_content, exec_type, 
    interface_name, method_name, task_code, param_type, extend, merge_rule_content, merge_time_range, retry_rule_content,
    is_delete, create_date, modify_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_task_conf
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByBizPartCodeAndBizCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_task_conf
        where biz_part_code = #{bizPartCode}
        and biz_code = #{bizCode}
        and is_delete = 0
        limit 1
    </select>

    <select id="selectByTaskCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_task_conf
        where task_code = #{taskCode}
    </select>

    <select id="selectByBizPartCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_task_conf
        where biz_part_code = #{bizPartCode}
        and is_delete = 0
    </select>
  <select id="findAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from biz_task_conf
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from biz_task_conf
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskConfDO" useGeneratedKeys="true">
    insert into biz_task_conf (biz_part_code, biz_part_name, biz_code, 
      biz_name, execute_rule_content, exec_type, 
      interface_name, method_name, param_type, extend, merge_rule_content,
      merge_time_range, retry_rule_content, is_delete, 
      create_date, modify_date)
    values (#{bizPartCode,jdbcType=VARCHAR}, #{bizPartName,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, 
      #{bizName,jdbcType=VARCHAR}, #{executeRuleContent,jdbcType=VARCHAR}, #{execType,jdbcType=INTEGER}, 
      #{interfaceName,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR}, #{paramType,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, #{mergeRuleContent,jdbcType=VARCHAR},
      #{mergeTimeRange,jdbcType=VARCHAR}, #{retryRuleContent,jdbcType=VARCHAR}, #{isDelete,jdbcType=BIGINT}, 
      #{createDate,jdbcType=TIMESTAMP}, #{modifyDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskConfDO" useGeneratedKeys="true">
    insert into biz_task_conf
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizPartCode != null">
        biz_part_code,
      </if>
      <if test="bizPartName != null">
        biz_part_name,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="bizName != null">
        biz_name,
      </if>
      <if test="executeRuleContent != null">
        execute_rule_content,
      </if>
      <if test="execType != null">
        exec_type,
      </if>
      <if test="interfaceName != null">
        interface_name,
      </if>
      <if test="methodName != null">
        method_name,
      </if>
      <if test="taskCode != null">
        task_code,
      </if>
      <if test="paramType != null">
        param_type,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="mergeRuleContent != null">
        merge_rule_content,
      </if>
      <if test="mergeTimeRange != null">
        merge_time_range,
      </if>
      <if test="retryRuleContent != null">
        retry_rule_content,
      </if>
      is_delete,create_date
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizPartCode != null">
        #{bizPartCode,jdbcType=VARCHAR},
      </if>
      <if test="bizPartName != null">
        #{bizPartName,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizName != null">
        #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="executeRuleContent != null">
        #{executeRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="execType != null">
        #{execType,jdbcType=INTEGER},
      </if>
      <if test="interfaceName != null">
        #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="taskCode != null">
        #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="mergeRuleContent != null">
        #{mergeRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="mergeTimeRange != null">
        #{mergeTimeRange,jdbcType=VARCHAR},
      </if>
      <if test="retryRuleContent != null">
        #{retryRuleContent,jdbcType=VARCHAR},
      </if>
      0,now()
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskConfDO">
    update biz_task_conf
    <set>
      <if test="bizPartCode != null">
        biz_part_code = #{bizPartCode,jdbcType=VARCHAR},
      </if>
      <if test="bizPartName != null">
        biz_part_name = #{bizPartName,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="bizName != null">
        biz_name = #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="executeRuleContent != null">
        execute_rule_content = #{executeRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="execType != null">
        exec_type = #{execType,jdbcType=INTEGER},
      </if>
      <if test="interfaceName != null">
        interface_name = #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="taskCode != null">
        task_code = #{taskCode,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        param_type = #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="mergeRuleContent != null">
        merge_rule_content = #{mergeRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="mergeTimeRange != null">
        merge_time_range = #{mergeTimeRange,jdbcType=VARCHAR},
      </if>
      <if test="retryRuleContent != null">
        retry_rule_content = #{retryRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyDate != null">
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskConfDO">
    update biz_task_conf
    set biz_part_code = #{bizPartCode,jdbcType=VARCHAR},
      biz_part_name = #{bizPartName,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      biz_name = #{bizName,jdbcType=VARCHAR},
      execute_rule_content = #{executeRuleContent,jdbcType=VARCHAR},
      exec_type = #{execType,jdbcType=INTEGER},
      interface_name = #{interfaceName,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      param_type = #{paramType,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR},
      merge_rule_content = #{mergeRuleContent,jdbcType=VARCHAR},
      merge_time_range = #{mergeTimeRange,jdbcType=VARCHAR},
      retry_rule_content = #{retryRuleContent,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIGINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>