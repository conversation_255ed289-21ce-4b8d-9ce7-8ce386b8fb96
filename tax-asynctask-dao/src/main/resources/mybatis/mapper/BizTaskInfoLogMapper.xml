<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.servyou.asynctask.dao.BizTaskInfoLogDAO">
  <resultMap id="BaseResultMap" type="cn.com.servyou.asynctask.dataobj.BizTaskLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="is_delete" jdbcType="BIGINT" property="isDelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, apply_date, is_delete, create_date, task_id, start_time, end_time, `status`
  </sql>

  <insert id="recordLog" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskLogDO" useGeneratedKeys="true">
    insert into biz_task_info_log (apply_date, is_delete, create_date, task_id, reason,
                              start_time, end_time, `status`
    )
    values (new(), 0, now(), #{taskId,jdbcType=BIGINT}, #{reason,jdbcType=VARCHAR},
            #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}
           )
  </insert>
</mapper>