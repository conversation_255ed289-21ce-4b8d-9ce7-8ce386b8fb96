<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.servyou.asynctask.dao.BizTaskInfoDAO">
  <resultMap id="BaseResultMap" type="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="apply_date" jdbcType="DATE" property="applyDate" />
    <result column="is_delete" jdbcType="BIGINT" property="isDelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="environment_code" jdbcType="VARCHAR" property="environmentCode" />
    <result column="biz_part_code" jdbcType="VARCHAR" property="bizPartCode" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="tax_no" jdbcType="VARCHAR" property="taxNo" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode"/>
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="parameters" jdbcType="VARCHAR" property="parameters" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="biz_tag" jdbcType="VARCHAR" property="bizTag" />
    <result column="exec_type" jdbcType="INTEGER" property="execType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="merge_biz_task_id" jdbcType="BIGINT" property="mergeBizTaskId" />
    <result column="end_flag" jdbcType="CHAR" property="endFlag" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="execute_num" jdbcType="INTEGER" property="executeNum" />
    <result column="execute_rule_content" jdbcType="VARCHAR" property="executeRuleContent" />
    <result column="lineup_ticket" jdbcType="VARCHAR" property="lineupTicket" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Base_Column_List">
    id, apply_date, is_delete, create_date, modify_date, environment_code, biz_part_code, biz_code,
    area_code, customer_id, tax_no, account_id, account_type, business_type, interface_name,
    method_name, `parameters`, param_type, biz_tag, exec_type, `status`, merge_biz_task_id, end_flag,
    reason, execute_num, execute_rule_content, lineup_ticket, remark, extend,task_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from biz_task_info
    where id = #{id,jdbcType=BIGINT}
    and apply_date = #{applyDate,jdbcType=DATE}
    and customer_id = #{customerId,jdbcType=VARCHAR}
  </select>

  <update id="updateTaskEndStatusById" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task_info
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="mergeBizTaskId != null">
        merge_biz_task_id = #{mergeBizTaskId,jdbcType=BIGINT},
      </if>
      end_flag = 1,
      modify_date = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
    and apply_date = #{applyDate,jdbcType=DATE}
    and customer_id = #{customerId,jdbcType=VARCHAR}
    and end_flag = 0
  </update>

  <update id="updateTaskEndStatusByCode" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task_info
    <set>
      `status` = #{status,jdbcType=INTEGER},
      <if test="mergeBizTaskId != null">
        merge_biz_task_id = #{mergeBizTaskId,jdbcType=BIGINT},
      </if>
      end_flag = 1,
      modify_date = now()
    </set>
    where apply_date = #{applyDate,jdbcType=DATE}
    and customer_id = #{customerId,jdbcType=VARCHAR}
    and biz_part_code = #{bizPartCode,jdbcType=VARCHAR}
    and biz_code = #{bizCode,jdbcType=VARCHAR}
    and area_code = #{areaCode,jdbcType=VARCHAR}
    and end_flag = 0
    and environment_code = #{environmentCode,jdbcType=VARCHAR}
    and `status` = 0
  </update>


  <update id="scheduleTask" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task_info
    <set>
      `status` = #{status,jdbcType=INTEGER},
      execute_num = execute_num + 1,
      modify_date = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
    and apply_date = #{applyDate,jdbcType=DATE}
    and customer_id = #{customerId,jdbcType=VARCHAR}
    and execute_num = #{executeNum,jdbcType=INTEGER}
    and end_flag = 0
  </update>

  <insert id="createTask" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO" useGeneratedKeys="true">
    insert into biz_task_info (apply_date, is_delete, create_date, modify_date,
    environment_code, biz_part_code, biz_code,
    area_code, customer_id, tax_no,
    account_id, account_type, business_type,
    interface_name, method_name, `parameters`, param_type,
    biz_tag, exec_type, `status`,
    merge_biz_task_id, end_flag, reason,
    execute_num, execute_rule_content, lineup_ticket,
    remark, extend,task_code)
    values (now(), 0, now(), now(),
    #{environmentCode,jdbcType=VARCHAR}, #{bizPartCode,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR},
    #{areaCode,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, #{taxNo,jdbcType=VARCHAR},
    #{accountId,jdbcType=VARCHAR}, #{accountType,jdbcType=INTEGER}, #{businessType,jdbcType=VARCHAR},
    #{interfaceName,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR}, #{parameters,jdbcType=VARCHAR},
    #{paramType,jdbcType=VARCHAR},
    #{bizTag,jdbcType=VARCHAR}, #{execType,jdbcType=INTEGER}, 0,
    null, 0, #{reason,jdbcType=VARCHAR},
    0, #{executeRuleContent,jdbcType=VARCHAR}, null,
    #{remark,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, #{taskCode,jdbcType=VARCHAR})
  </insert>


  <update id="updateTaskStatus" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task_info
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="endFlag != null">
        end_flag = #{endFlag,jdbcType=CHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and apply_date = #{applyDate,jdbcType=DATE}
    and customer_id = #{customerId,jdbcType=VARCHAR}
  </update>


  <update id="updateTaskTicket" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task_info
    set lineup_ticket = #{lineupTicket,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
      and apply_date = #{applyDate,jdbcType=DATE}
      and customer_id = #{customerId,jdbcType=VARCHAR}
      and end_flag = 0
  </update>

  <update id="updateTaskExtend" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task_info
    set extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
      and apply_date = #{applyDate,jdbcType=DATE}
      and customer_id = #{customerId,jdbcType=VARCHAR}
      and end_flag = 0
  </update>
</mapper>