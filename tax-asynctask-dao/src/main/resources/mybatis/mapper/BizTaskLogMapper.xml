<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.servyou.asynctask.dao.BizTaskLogDAO">
  <resultMap id="BaseResultMap" type="cn.com.servyou.asynctask.dataobj.BizTaskLogDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="BIGINT" property="isDelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    id, is_delete, create_date, task_id, start_time, end_time, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_task_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from biz_task_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskLogDO" useGeneratedKeys="true">
    insert into biz_task_log (is_delete, create_date, task_id, 
      start_time, end_time, `status`
      )
    values (0, now(), #{taskId,jdbcType=BIGINT},
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskLogDO" useGeneratedKeys="true">
    insert into biz_task_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskLogDO">
    update biz_task_log
    <set>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskLogDO">
    update biz_task_log
    set is_delete = #{isDelete,jdbcType=BIGINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="recordLog" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskLogDO" useGeneratedKeys="true">
    insert into biz_task_log (is_delete, create_date, task_id, reason,
                              start_time, end_time, `status`
    )
    values (0, now(), #{taskId,jdbcType=BIGINT}, #{reason,jdbcType=VARCHAR},
            #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}
           )
  </insert>
</mapper>