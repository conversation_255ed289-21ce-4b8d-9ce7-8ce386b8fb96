<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.servyou.asynctask.dao.BizTaskDAO">
  <resultMap id="BaseResultMap" type="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_delete" jdbcType="BIGINT" property="isDelete" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="environment_code" jdbcType="VARCHAR" property="environmentCode" />
    <result column="biz_part_code" jdbcType="VARCHAR" property="bizPartCode" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="tax_no" jdbcType="VARCHAR" property="taxNo" />
    <result column="account_id" jdbcType="VARCHAR" property="accountId" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode"/>
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="parameters" jdbcType="VARCHAR" property="parameters" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="biz_tag" jdbcType="VARCHAR" property="bizTag" />
    <result column="exec_type" jdbcType="INTEGER" property="execType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="merge_biz_task_id" jdbcType="BIGINT" property="mergeBizTaskId" />
    <result column="end_flag" jdbcType="CHAR" property="endFlag" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="execute_num" jdbcType="INTEGER" property="executeNum" />
    <result column="execute_rule_content" jdbcType="VARCHAR" property="executeRuleContent" />
    <result column="lineup_ticket" jdbcType="VARCHAR" property="lineupTicket" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Base_Column_List">
    id, is_delete, create_date, modify_date, environment_code, biz_part_code, biz_code,
    area_code, customer_id, tax_no, account_id, account_type, business_type, interface_name,
    method_name, `parameters`, param_type, biz_tag, exec_type, `status`, merge_biz_task_id, end_flag,
    reason, execute_num, execute_rule_content, lineup_ticket, remark, extend,task_code
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from biz_task
    where id = #{id,jdbcType=BIGINT}
    and customer_id = #{customerId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from biz_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO" useGeneratedKeys="true">
    insert into biz_task (is_delete, create_date, modify_date,
    environment_code, biz_part_code, biz_code,
    area_code, customer_id, tax_no,
    account_id, account_type, business_type,
    interface_name, method_name, `parameters`, param_type,
    biz_tag, exec_type, `status`,
    merge_biz_task_id, end_flag, reason,
    execute_num, execute_rule_content, lineup_ticket,
    remark, extend,task_code)
    values (#{isDelete,jdbcType=BIGINT}, #{createDate,jdbcType=TIMESTAMP}, #{modifyDate,jdbcType=TIMESTAMP},
    #{environmentCode,jdbcType=VARCHAR}, #{bizPartCode,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR},
    #{areaCode,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, #{taxNo,jdbcType=VARCHAR},
    #{accountId,jdbcType=VARCHAR}, #{accountType,jdbcType=INTEGER}, #{businessType,jdbcType=VARCHAR},
    #{interfaceName,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR}, #{parameters,jdbcType=VARCHAR},
    #{paramType,jdbcType=VARCHAR},
    #{bizTag,jdbcType=VARCHAR}, #{execType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
    #{mergeBizTaskId,jdbcType=BIGINT}, #{endFlag,jdbcType=CHAR}, #{reason,jdbcType=VARCHAR},
    #{executeNum,jdbcType=INTEGER}, #{executeRuleContent,jdbcType=VARCHAR}, #{lineupTicket,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, #{taskCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO" useGeneratedKeys="true">
    insert into biz_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
      <if test="environmentCode != null">
        environment_code,
      </if>
      <if test="bizPartCode != null">
        biz_part_code,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="areaCode != null">
        area_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="taxNo != null">
        tax_no,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="interfaceName != null">
        interface_name,
      </if>
      <if test="methodName != null">
        method_name,
      </if>
      <if test="parameters != null">
        `parameters`,
      </if>
      <if test="paramType != null">
        param_type,
      </if>
      <if test="bizTag != null">
        biz_tag,
      </if>
      <if test="execType != null">
        exec_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="mergeBizTaskId != null">
        merge_biz_task_id,
      </if>
      <if test="endFlag != null">
        end_flag,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="executeNum != null">
        execute_num,
      </if>
      <if test="executeRuleContent != null">
        execute_rule_content,
      </if>
      <if test="lineupTicket != null">
        lineup_ticket,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="environmentCode != null">
        #{environmentCode,jdbcType=VARCHAR},
      </if>
      <if test="bizPartCode != null">
        #{bizPartCode,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="taxNo != null">
        #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="interfaceName != null">
        #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="parameters != null">
        #{parameters,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="bizTag != null">
        #{bizTag,jdbcType=VARCHAR},
      </if>
      <if test="execType != null">
        #{execType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="mergeBizTaskId != null">
        #{mergeBizTaskId,jdbcType=BIGINT},
      </if>
      <if test="endFlag != null">
        #{endFlag,jdbcType=CHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="executeNum != null">
        #{executeNum,jdbcType=INTEGER},
      </if>
      <if test="executeRuleContent != null">
        #{executeRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="lineupTicket != null">
        #{lineupTicket,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    <set>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIGINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyDate != null">
        modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="environmentCode != null">
        environment_code = #{environmentCode,jdbcType=VARCHAR},
      </if>
      <if test="bizPartCode != null">
        biz_part_code = #{bizPartCode,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="areaCode != null">
        area_code = #{areaCode,jdbcType=VARCHAR},
      </if>
<!--      <if test="customerId != null">-->
<!--        customer_id = #{customerId,jdbcType=VARCHAR},-->
<!--      </if>-->
      <if test="taxNo != null">
        tax_no = #{taxNo,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="interfaceName != null">
        interface_name = #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="parameters != null">
        `parameters` = #{parameters,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        param_type = #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="bizTag != null">
        biz_tag = #{bizTag,jdbcType=VARCHAR},
      </if>
      <if test="execType != null">
        exec_type = #{execType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="mergeBizTaskId != null">
        merge_biz_task_id = #{mergeBizTaskId,jdbcType=BIGINT},
      </if>
      <if test="endFlag != null">
        end_flag = #{endFlag,jdbcType=CHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="executeNum != null">
        execute_num = #{executeNum,jdbcType=INTEGER},
      </if>
      <if test="executeRuleContent != null">
        execute_rule_content = #{executeRuleContent,jdbcType=VARCHAR},
      </if>
      <if test="lineupTicket != null">
        lineup_ticket = #{lineupTicket,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and customer_id = #{customerId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    set is_delete = #{isDelete,jdbcType=BIGINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      modify_date = #{modifyDate,jdbcType=TIMESTAMP},
      environment_code = #{environmentCode,jdbcType=VARCHAR},
      biz_part_code = #{bizPartCode,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      area_code = #{areaCode,jdbcType=VARCHAR},
--       customer_id = #{customerId,jdbcType=VARCHAR},
      tax_no = #{taxNo,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=VARCHAR},
      interface_name = #{interfaceName,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      `parameters` = #{parameters,jdbcType=VARCHAR},
      param_type = #{paramType,jdbcType=VARCHAR},
      biz_tag = #{bizTag,jdbcType=VARCHAR},
      exec_type = #{execType,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      merge_biz_task_id = #{mergeBizTaskId,jdbcType=BIGINT},
      end_flag = #{endFlag,jdbcType=CHAR},
      reason = #{reason,jdbcType=VARCHAR},
      execute_num = #{executeNum,jdbcType=INTEGER},
      execute_rule_content = #{executeRuleContent,jdbcType=VARCHAR},
      lineup_ticket = #{lineupTicket,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
    and customer_id = #{customerId,jdbcType=VARCHAR}
  </update>

  <update id="updateTaskEndStatusById" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="mergeBizTaskId != null">
        merge_biz_task_id = #{mergeBizTaskId,jdbcType=BIGINT},
      </if>
      end_flag = 1,
      modify_date = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
    and customer_id = #{customerId,jdbcType=VARCHAR}
    and end_flag = 0
  </update>

  <update id="updateTaskEndStatusByCode" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    <set>
      `status` = #{status,jdbcType=INTEGER},
      <if test="mergeBizTaskId != null">
        merge_biz_task_id = #{mergeBizTaskId,jdbcType=BIGINT},
      </if>
      end_flag = 1,
      modify_date = now()
    </set>
    where customer_id = #{customerId,jdbcType=VARCHAR}
    and biz_part_code = #{bizPartCode,jdbcType=VARCHAR}
    and biz_code = #{bizCode,jdbcType=VARCHAR}
    and area_code = #{areaCode,jdbcType=VARCHAR}
    and end_flag = 0
    and environment_code = #{environmentCode,jdbcType=VARCHAR}
    and `status` = 0
  </update>


  <update id="scheduleTask" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    <set>
      `status` = #{status,jdbcType=INTEGER},
      execute_num = execute_num + 1,
      modify_date = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
    and customer_id = #{customerId,jdbcType=VARCHAR}
    and execute_num = #{executeNum,jdbcType=INTEGER}
    and end_flag = 0
  </update>

  <insert id="createTask" keyColumn="id" keyProperty="id" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO" useGeneratedKeys="true">
    insert into biz_task (is_delete, create_date, modify_date,
    environment_code, biz_part_code, biz_code,
    area_code, customer_id, tax_no,
    account_id, account_type, business_type,
    interface_name, method_name, `parameters`, param_type,
    biz_tag, exec_type, `status`,
    merge_biz_task_id, end_flag, reason,
    execute_num, execute_rule_content, lineup_ticket,
    remark, extend,task_code)
    values (0, now(), now(),
    #{environmentCode,jdbcType=VARCHAR}, #{bizPartCode,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR},
    #{areaCode,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, #{taxNo,jdbcType=VARCHAR},
    #{accountId,jdbcType=VARCHAR}, #{accountType,jdbcType=INTEGER}, #{businessType,jdbcType=VARCHAR},
    #{interfaceName,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR}, #{parameters,jdbcType=VARCHAR},
    #{paramType,jdbcType=VARCHAR},
    #{bizTag,jdbcType=VARCHAR}, #{execType,jdbcType=INTEGER}, 0,
    null, 0, #{reason,jdbcType=VARCHAR},
    0, #{executeRuleContent,jdbcType=VARCHAR}, null,
    #{remark,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, #{taskCode,jdbcType=VARCHAR})
  </insert>


  <update id="updateTaskStatus" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="endFlag != null">
        end_flag = #{endFlag,jdbcType=CHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    and customer_id = #{customerId,jdbcType=VARCHAR}
  </update>


  <update id="updateTaskTicket" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    set lineup_ticket = #{lineupTicket,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
      and customer_id = #{customerId,jdbcType=VARCHAR}
      and end_flag = 0
  </update>

  <update id="updateTaskExtend" parameterType="cn.com.servyou.asynctask.dataobj.BizTaskDO">
    update biz_task
    set extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
      and customer_id = #{customerId,jdbcType=VARCHAR}
      and end_flag = 0
  </update>
</mapper>