<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.servyou.asynctask.dao.TestDAO">
    <resultMap id="ResultMap" type="cn.com.servyou.asynctask.dataobj.TestDO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="select" resultMap="ResultMap">
        SELECT 1
        FROM dual
    </select>

</mapper>