<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- maven模型版本 maven2/maven3 使用4.0.0-->
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.servyou</groupId>
        <artifactId>tax-asynctask</artifactId>
        <version>${version.number}-${build.number}</version>
    </parent>

    <!-- 项目坐标 -->
    <groupId>cn.com.servyou</groupId>
    <artifactId>tax-asynctask-facade</artifactId>

    <properties>
        <i7boot.version>3.3.0-SNAPSHOT</i7boot.version>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!-- framework dependencies -->
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-dubbo-rpc-facade</artifactId>
            <version>${i7boot.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.14</version>
            <optional>true</optional>
        </dependency>
        <!-- framework dependencies -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <compilerVersion>${java.version}</compilerVersion>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <!-- prevents endPosTable exception for maven compile -->
                    <useIncrementalCompilation>false</useIncrementalCompilation>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <!-- 构件管理 -->
    <distributionManagement>
        <!-- release仓库 -->
        <repository>
            <id>xqy-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://maven.dc.servyou-it.com/nexus/content/repositories/xqy/</url>
        </repository>
        <!-- snapshot仓库 版本尾缀为-SNAPSHOT的组件会从该地址上传或下载 -->
        <snapshotRepository>
            <id>xqy-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://maven.dc.servyou-it.com/nexus/content/repositories/xiaoyiqe-test/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
