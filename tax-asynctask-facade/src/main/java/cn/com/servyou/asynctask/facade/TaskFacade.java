package cn.com.servyou.asynctask.facade;

import cn.com.servyou.asynctask.facade.dto.TaskReqDTO;
import cn.com.servyou.asynctask.facade.dto.TaskRespDTO;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.framework.rpc.facade.VoidResult;

/**
 * <AUTHOR>
 * @description: TaskFacade接口类
 * @since 2024/8/15
 */
public interface TaskFacade {

    SingleResult<TaskRespDTO> createTask(TaskReqDTO taskReqDTO);

    VoidResult cancelTask(TaskReqDTO taskReqDTO);
}
