package cn.com.servyou.asynctask.facade;

import cn.com.servyou.asynctask.facade.dto.TestDTO;
import cn.com.servyou.xqy.framework.rpc.facade.ListResult;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;

public interface TestFacade {

    /**
    * Facade层接口返回数据结构为Result或其子类
    * 单数据对象返回SingleResult
    * 列表返回ListResult
    * void接口返回VoidResult
    *
    * @return Result及其子类
    */
    ListResult<TestDTO> get();

    /**
    * redis 使用说明
    *
    * @return redis
    */
    SingleResult<String> testRedis();
}
