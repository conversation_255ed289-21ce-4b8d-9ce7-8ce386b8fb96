package cn.com.servyou.asynctask.facade.dto;

import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 任务请求DTO
 * @since 2024/8/15
 */
@Data
public class TaskReqDTO extends DTO {

    /**
     * 用户ID
     */
    private String accountId;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * 地区数字代码
     */
    private String areaCode;

    /**
     * 业务方代码
     */
    private String bizPartCode;

    /**
     * 业务代码
     */
    private String bizCode;

    /**
     * 税局账户类型:开票1051必传，其他可选
     */
    private Integer accountType;

    /**
     * 业务类型：票家家必传，其他可选
     */
    private String bizType;

    /**
     * 业务参数
     */
    private String bizParams;

    /**
     * 任务ID
     */
    private Long bizTaskId;

    /**
     * 任务执行结果MQtag后缀
     */
    private String bizMqTag;

    /**
     * 执行规则
     */
    private String execRule;

    /**
     * 备注（长度1024）
     */
    private String remark;

    /**
     * 任务代码
     */
    private String taskCode;

    /**
     * 唯一键值标识，如果不为空，并发时，取同一个结果
     */
    private String uniqueKeyValue;
    /**
     * 执行类型 0 预排队
     * 1 预执行
     * 2 不处理排队
     */
    private Integer execType;

}
