package cn.com.servyou.asynctask.facade.dto;

import cn.com.servyou.xqy.framework.rpc.facade.DTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 任务返回DTO
 * @since 2024/8/15
 */
@Data
public class TaskRespDTO extends DTO {

    /**
     * 任务ID
     */
    private Long bizTaskId;

    public static TaskRespDTO buildSuccess(Long bizTaskId) {
        TaskRespDTO taskRespDTO = new TaskRespDTO();
        taskRespDTO.setBizTaskId(bizTaskId);
        return taskRespDTO;
    }
}
