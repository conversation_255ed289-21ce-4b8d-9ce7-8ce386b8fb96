package cn.com.servyou.asynctask.facadeimpl;

import cn.com.servyou.asynctask.core.domain.AsyncTaskCancelDTO;
import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.asynctask.core.service.BizTaskConfService;
import cn.com.servyou.asynctask.core.service.BizTaskService;
import cn.com.servyou.asynctask.core.utils.AssertUtil;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.com.servyou.asynctask.facade.TaskFacade;
import cn.com.servyou.asynctask.facade.dto.TaskReqDTO;
import cn.com.servyou.asynctask.facade.dto.TaskRespDTO;
import cn.com.servyou.asynctask.shared.AsyncTaskScheduleManagement;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.framework.rpc.facade.VoidResult;
import cn.com.servyou.xqy.framework.rpc.facadeimpl.ResultServiceCallback;
import cn.com.servyou.xqy.framework.rpc.facadeimpl.ServiceTemplate;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: TaskFacadeImpl实现类
 * @since 2024/8/15
 */
@Slf4j
@Service
public class TaskFacadeImpl implements TaskFacade {

    @Autowired
    private ServiceTemplate serviceTemplate;
    @Autowired
    private BizTaskService bizTaskService;
    @Autowired
    private BizTaskConfService bizTaskConfService;
    @Autowired
    private AsyncTaskScheduleManagement asyncTaskScheduleManagement;

    /**
     * 调用创建接口提交任务-必要参数校验-落库
     * 执行【任务合并】
     * 触发【任务调度】
     *
     * @param taskReqDTO
     * @return
     */
    @Override
    public SingleResult<TaskRespDTO> createTask(TaskReqDTO taskReqDTO) {

        return serviceTemplate.executeWithoutTx("创建任务", new ResultServiceCallback<SingleResult<TaskRespDTO>>() {

            @Override
            public SingleResult<TaskRespDTO> createDefaultResult() {
                return SingleResult.success(new TaskRespDTO());
            }

            @Override
            public void executeService(SingleResult<TaskRespDTO> singleResult) {
                checkTaskReqDTO(taskReqDTO);
                ScheduleLogUtils.log("创建任务", null, taskReqDTO.getCustomerId(),
                        "req={}", JSONUtil.toJsonStr(taskReqDTO));
                TaskRespDTO taskRespDTO = asyncTaskScheduleManagement.createTask(taskReqDTO);
                singleResult.setEntity(taskRespDTO);
            }

        });
    }

    private void checkTaskReqDTO(TaskReqDTO taskReqDTO) {

        AssertUtil.notNull(taskReqDTO, ErrorCodeEnum.COMMON_PARAM_NOT_EMPTY);
        AssertUtil.notNull(taskReqDTO.getCustomerId(), ErrorCodeEnum.COMPANY_ID_NOT_EMPTY);
        AssertUtil.notEmpty(ErrorCodeEnum.TAX_NO_NOT_EMPTY, taskReqDTO.getTaxNo());
        AssertUtil.notEmpty(ErrorCodeEnum.AREA_CODE_NOT_EMPTY, taskReqDTO.getAreaCode());
        AssertUtil.notEmpty(ErrorCodeEnum.BIZ_PART_CODE_NOT_EMPTY, taskReqDTO.getBizPartCode());
        AssertUtil.notEmpty(ErrorCodeEnum.BIZ_CODE_NOT_EMPTY, taskReqDTO.getBizCode());

    }

    @Override
    public VoidResult cancelTask(TaskReqDTO taskReqDTO) {
        return serviceTemplate.executeWithTx("取消任务", new ResultServiceCallback<VoidResult>() {
            @Override
            public VoidResult createDefaultResult() {
                return VoidResult.success();
            }

            @Override
            public void executeService(VoidResult voidResult) {
                AsyncTaskCancelDTO asyncTaskCancelDTO = new AsyncTaskCancelDTO();
                asyncTaskCancelDTO.setAreaCode(taskReqDTO.getAreaCode());
                asyncTaskCancelDTO.setBizPartCode(taskReqDTO.getBizPartCode());
                asyncTaskCancelDTO.setBizCode(taskReqDTO.getBizCode());
                asyncTaskCancelDTO.setBizTaskId(taskReqDTO.getBizTaskId());
                asyncTaskCancelDTO.setCustomerId(taskReqDTO.getCustomerId());
                bizTaskService.cancelTask(asyncTaskCancelDTO);
            }
        });
    }
}
