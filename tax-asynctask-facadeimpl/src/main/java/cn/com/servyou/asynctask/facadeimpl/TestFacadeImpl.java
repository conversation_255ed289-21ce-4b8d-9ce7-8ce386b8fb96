package cn.com.servyou.asynctask.facadeimpl;

import cn.com.servyou.asynctask.core.service.TestService;
import cn.com.servyou.asynctask.facade.TestFacade;
import cn.com.servyou.asynctask.facade.dto.TestDTO;
import cn.com.servyou.asynctask.facadeimpl.convert.TestFacadeConvert;
import cn.com.servyou.xqy.framework.rpc.facade.ListResult;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.framework.rpc.facadeimpl.ResultServiceCallback;
import cn.com.servyou.xqy.framework.rpc.facadeimpl.ServiceTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Service
public class TestFacadeImpl implements TestFacade {

    /**
    * 业务模板，方便块事务控制
    */
    @Autowired
    private ServiceTemplate serviceTemplate;

    @Autowired
    private TestService testService;

    @Override
    public ListResult<TestDTO> get() {
        // 当需要块事务时，使用executeWithTx
        return serviceTemplate.executeWithoutTx("测试业务", new ResultServiceCallback<ListResult<TestDTO>>() {
            @Override
            public ListResult<TestDTO> createDefaultResult() {
                return ListResult.success(new ArrayList<TestDTO>());
            }

             @Override
             public void executeService(ListResult<TestDTO> result) {
                 result.setList(TestFacadeConvert.toList(testService.get()));
             }
        });
    }

    @Override
    public SingleResult<String> testRedis() {
        return serviceTemplate.executeWithoutTx("测试redis", new ResultServiceCallback<SingleResult<String>>() {
            @Override
            public SingleResult<String> createDefaultResult() {
                return SingleResult.success(null);
             }

            @Override
            public void executeService(SingleResult<String> result) {
                result.setEntity(testService.testRedis());
            }
        });
    }
}
