package cn.com.servyou.asynctask.facadeimpl.convert;

import cn.com.servyou.asynctask.core.domain.Test;
import cn.com.servyou.asynctask.facade.dto.TestDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class TestFacadeConvert {

    /**
     * Test to TestDTO
     *
     * @param test Test
     * @return TestDTO
     */
    public static TestDTO toTestDTO(Test test) {
        TestDTO testDTO = new TestDTO();
        if (null == test) {
            return testDTO;
        }
        testDTO.setId(test.getId());
        testDTO.setName(test.getName());
        return testDTO;
    }

    /**
     * List<Test> to List<TestDTO>
     *
     * @param testList List<Test>
     * @return List<TestDTO>
     */
    public static List<TestDTO> toList(List<Test> testList) {
        List<TestDTO> testDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(testList)) {
            return testDTOList;
        }
        for (Test test : testList) {
            testDTOList.add(toTestDTO(test));
        }
        return testDTOList;
    }

}
