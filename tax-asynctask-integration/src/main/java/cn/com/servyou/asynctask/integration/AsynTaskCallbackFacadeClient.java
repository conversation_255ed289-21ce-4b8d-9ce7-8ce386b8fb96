package cn.com.servyou.asynctask.integration;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/2/26 10:00
 */
public interface AsynTaskCallbackFacadeClient {
    /**
     * 清册taxbase通用结果通知
     * @param resultNotifyReqDTO
     * @return
     */
    ResultNotifyRespDTO callback(ResultNotifyReqDTO resultNotifyReqDTO);
}
