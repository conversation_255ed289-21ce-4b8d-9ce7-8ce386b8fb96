package cn.com.servyou.asynctask.integration;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.taxbase.facade.dto.SelfCheckParamDTO;
import cn.com.servyou.taxbase.facade.dto.declaration.DeclarationSyncTaskParamDTO;
import cn.com.servyou.taxbase.facade.dto.declaration.InitOrQueryDTO;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.framework.rpc.facade.VoidResult;

public interface DeclarationFacadeClient {

    @Deprecated
    void queryOrInit(InitOrQueryDTO initOrQueryDTO);

    /**
     * 更新清册
     * @param declarationSyncTaskParamDTO
     */
    void excuteSyncStatusTask(DeclarationSyncTaskParamDTO declarationSyncTaskParamDTO);

    /**
     * 第三方未申报截图结果回调给业务方
     * @param resultNotifyReqDTO
     * @return
     */
    ResultNotifyRespDTO cutPictureForDsfWsbCallback(ResultNotifyReqDTO resultNotifyReqDTO);

    /**
     * 自检
     * @param selfCheckParamDTO
     */
    void selfCheck(SelfCheckParamDTO selfCheckParamDTO);



}
