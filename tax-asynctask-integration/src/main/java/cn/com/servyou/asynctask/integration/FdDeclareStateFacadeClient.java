package cn.com.servyou.asynctask.integration;

import cn.com.servyou.xqy.gateway.dto.qcxx.FdCutPictuteForDsfWsbRequest;
import cn.com.servyou.xqy.gateway.dto.qcxx.TbcGetMergeResultRequest;
import cn.com.servyou.xqy.gateway.dto.sfzrd.FdSfzrdRequestDTO;
import cn.com.servyou.xqy.gateway.dto.sfzrd.SfzrdResponse;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/16 16:06
 */
public interface FdDeclareStateFacadeClient {

    /**
     * 税费种更新查询
     * @param fdSfzrdRequestDTO
     */
    void querySfzDataNew(FdSfzrdRequestDTO fdSfzrdRequestDTO);

    /**
     * 第三方未申报截图触发接口
     * @param fdCutPictuteForDsfWsbRequest
     */
    void cutPictureForDsfWsb(FdCutPictuteForDsfWsbRequest fdCutPictuteForDsfWsbRequest);

    /**
     * TBC
     * @param tbcGetMergeResultRequest
     */
    void tbcGetMergeResult(TbcGetMergeResultRequest tbcGetMergeResultRequest);
}

