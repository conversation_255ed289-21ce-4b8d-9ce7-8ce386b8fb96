package cn.com.servyou.asynctask.integration;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/16 15:50
 */
public interface TaxIdentificationFacadeClient {

    /**
     * 通知税费种执行结果
     * @param resultNotifyReqDTO
     * @return
     */
    ResultNotifyRespDTO taxIdentificationDataCallback(ResultNotifyReqDTO resultNotifyReqDTO);
}
