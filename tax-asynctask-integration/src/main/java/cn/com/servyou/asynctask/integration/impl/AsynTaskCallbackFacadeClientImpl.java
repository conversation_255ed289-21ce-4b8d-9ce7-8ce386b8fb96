package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.integration.AsynTaskCallbackFacadeClient;
import cn.com.servyou.taxbase.facade.AsynTaskCallbackFacade;
import cn.com.servyou.xqy.common.utils.RpcResultUtils;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/2/26 10:00
 */
@Service
@Slf4j
public class AsynTaskCallbackFacadeClientImpl implements AsynTaskCallbackFacadeClient {

    @Autowired
    private AsynTaskCallbackFacade asynTaskCallbackFacade;

    @Override
    public ResultNotifyRespDTO callback(ResultNotifyReqDTO resultNotifyReqDTO) {
        SingleResult<ResultNotifyRespDTO> singleResult =
                asynTaskCallbackFacade.callback(resultNotifyReqDTO);
        return RpcResultUtils.parseResult(singleResult);
    }
}
