package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.integration.BatchDataTaskFacadeClient;
import cn.com.servyou.taxbase.facade.BatchDataTaskFacade;
import cn.com.servyou.taxbase.facade.dto.LoginTaskParamsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/7/23 18:34
 */
@Service
@Slf4j
public class BatchDataTaskFacadeClientImpl implements BatchDataTaskFacadeClient {

    @Autowired
    private BatchDataTaskFacade batchDataTaskFacade;


    @Override
    public void excuteLoginTask(LoginTaskParamsDTO loginTaskParamsDTO) {
        batchDataTaskFacade.excuteLoginTask(loginTaskParamsDTO);
    }
}
