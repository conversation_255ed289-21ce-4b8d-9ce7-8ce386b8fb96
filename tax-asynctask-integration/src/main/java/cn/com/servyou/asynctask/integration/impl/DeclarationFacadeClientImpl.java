package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.integration.DeclarationFacadeClient;
import cn.com.servyou.taxbase.facade.DeclarationFacade;
import cn.com.servyou.taxbase.facade.dto.SelfCheckParamDTO;
import cn.com.servyou.taxbase.facade.dto.declaration.DeclarationSyncTaskParamDTO;
import cn.com.servyou.taxbase.facade.dto.declaration.InitOrQueryDTO;
import cn.com.servyou.xqy.common.utils.RpcResultUtils;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DeclarationFacadeClientImpl implements DeclarationFacadeClient {
    @Autowired
    private DeclarationFacade declarationFacade;

    @Override
    public void queryOrInit(InitOrQueryDTO initOrQueryDTO) {
        declarationFacade.queryOrInit(initOrQueryDTO);
    }

    @Override
    public void excuteSyncStatusTask(DeclarationSyncTaskParamDTO declarationSyncTaskParamDTO) {
        declarationFacade.excuteSyncStatusTask(declarationSyncTaskParamDTO);
    }

    @Override
    public ResultNotifyRespDTO cutPictureForDsfWsbCallback(ResultNotifyReqDTO resultNotifyReqDTO) {
        SingleResult<ResultNotifyRespDTO> singleResult = declarationFacade.cutPictureForDsfWsbCallback(resultNotifyReqDTO);
        return RpcResultUtils.parseResult(singleResult);
    }

    @Override
    public void selfCheck(SelfCheckParamDTO selfCheckParamDTO) {
        declarationFacade.selfCheck(selfCheckParamDTO);
    }


}
