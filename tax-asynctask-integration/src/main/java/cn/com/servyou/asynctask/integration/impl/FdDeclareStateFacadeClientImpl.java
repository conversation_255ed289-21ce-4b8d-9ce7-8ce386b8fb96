package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.integration.FdDeclareStateFacadeClient;
import cn.com.servyou.xqy.gateway.dto.qcxx.FdCutPictuteForDsfWsbRequest;
import cn.com.servyou.xqy.gateway.dto.qcxx.TbcGetMergeResultRequest;
import cn.com.servyou.xqy.gateway.dto.sfzrd.FdSfzrdRequestDTO;
import cn.com.servyou.xqy.gateway.facade.FdDeclareStateFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/16 16:07
 */
@Service
@Slf4j
public class FdDeclareStateFacadeClientImpl implements FdDeclareStateFacadeClient {
    @Autowired
    private FdDeclareStateFacade fdDeclareStateFacade;
    @Override
    public void querySfzDataNew(FdSfzrdRequestDTO fdSfzrdRequestDTO) {
        fdDeclareStateFacade.querySfzDataNew(fdSfzrdRequestDTO);
    }

    @Override
    public void cutPictureForDsfWsb(FdCutPictuteForDsfWsbRequest fdCutPictuteForDsfWsbRequest) {
        fdDeclareStateFacade.cutPictureForDsfWsb(fdCutPictuteForDsfWsbRequest);
    }

    @Override
    public void tbcGetMergeResult(TbcGetMergeResultRequest tbcGetMergeResultRequest) {
        fdDeclareStateFacade.tbcGetMergeResult(tbcGetMergeResultRequest);
    }
}
