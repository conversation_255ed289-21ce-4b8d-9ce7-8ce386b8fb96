package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.integration.TaskTicketClient;
import cn.com.servyou.taxlineup.facade.TaskTicketFacade;
import cn.com.servyou.taxlineup.facade.dto.InLineReqDTO;
import cn.com.servyou.taxlineup.facade.dto.InLineRespDTO;
import cn.com.servyou.xqy.framework.exception.BusinessException;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.framework.rpc.facade.VoidResult;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 排队任务客户端实现类
 * @since 2024/8/19
 */
@Slf4j
@Service
public class TaskTicketClientImpl implements TaskTicketClient {

    @Autowired
    private TaskTicketFacade taskTicketFacade;

    @Override
    public InLineRespDTO getTicket(InLineReqDTO inlineReqDTO) {

        SingleResult<InLineRespDTO> singleResult = taskTicketFacade.getTicket(inlineReqDTO);
        log.info("调用排队系统排队, inlineReqDTO={},  result={}", JSONUtil.toJsonStr(inlineReqDTO),
                JSONUtil.toJsonStr(singleResult));
        if (!singleResult.isSuccess()) {
            log.error("调用排队系统排队报错.  result={}",  singleResult.getErrorContext().toString());
            throw new BusinessException(singleResult.getErrorContext().getErrorCode(), singleResult.getErrorContext().getErrorMessage());
        }
        return singleResult.getEntity();
    }

    @Override
    public void releaseTicket(String ticket) {
        VoidResult result = taskTicketFacade.releaseTicket(ticket);
        log.info("释放ticket ,ticket为:{}", ticket);
        if (!result.isSuccess()) {
            log.error("释放ticket失败:{}", result.getErrorContext().toString());
            throw new BusinessException(result.getErrorContext().getErrorCode(), result.getErrorContext().getErrorMessage());
        }
    }
}
