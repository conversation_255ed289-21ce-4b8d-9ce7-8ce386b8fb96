package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.integration.TaxIdentificationFacadeClient;
import cn.com.servyou.taxbase.facade.TaxIdentificationFacade;
import cn.com.servyou.xqy.common.utils.RpcResultUtils;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/16 15:52
 */
@Service
@Slf4j
public class TaxIdentificationFacadeClientImpl implements TaxIdentificationFacadeClient {

    @Autowired
    private TaxIdentificationFacade taxIdentificationFacade;

    @Override
    public ResultNotifyRespDTO taxIdentificationDataCallback(ResultNotifyReqDTO resultNotifyReqDTO) {
        SingleResult<ResultNotifyRespDTO> singleResult =
                taxIdentificationFacade.taxIdentificationDataCallback(resultNotifyReqDTO);
        return RpcResultUtils.parseResult(singleResult);
    }
}
