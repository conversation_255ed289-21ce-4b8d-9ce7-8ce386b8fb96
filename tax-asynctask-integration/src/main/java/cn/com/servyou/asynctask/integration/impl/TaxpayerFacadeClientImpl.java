package cn.com.servyou.asynctask.integration.impl;

import cn.com.servyou.asynctask.integration.TaxpayerFacadeClient;
import cn.com.servyou.xqy.common.utils.RpcResultUtils;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.taxcore.facade.basic.taxpayer.TaxpayerFacade;
import cn.com.servyou.xqy.taxcore.facade.basic.taxpayer.dto.TaxpayerDTO;
import cn.com.servyou.xqy.taxcore.facade.basic.taxpayer.dto.TaxpayerTaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2025/3/20 16:13
 */
@Service
@Slf4j
public class TaxpayerFacadeClientImpl implements TaxpayerFacadeClient {

    @Autowired
    private TaxpayerFacade taxpayerFacade;

    @Override
    public void getTaxpayerByTaxNoAndAreaCodeCurrentMonthForTask(TaxpayerTaskDTO dto) {
        taxpayerFacade.getTaxpayerByTaxNoAndAreaCodeCurrentMonthForTask(dto);
    }
}
