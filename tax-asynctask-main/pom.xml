<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- maven模型版本 maven2/maven3 使用4.0.0-->
    <modelVersion>4.0.0</modelVersion>
    <!-- 父级项目坐标 (默认从本地查找,未找到再到远程构件仓库查找)-->
    <parent>
        <groupId>cn.com.servyou</groupId>
        <artifactId>tax-asynctask</artifactId>
        <version>${version.number}-${build.number}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <!-- 项目坐标 -->
    <artifactId>tax-asynctask-main</artifactId>
    <!-- 打包方式 -->
    <packaging>jar</packaging>

    <dependencies>
        <!-- framework dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-shared</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-mybatis-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-facadeimpl</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-msg</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.7.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <!-- 17boot dependencies -->
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-dubbo-rpc-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-iris-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-mybatis-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-log</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-mq-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-security</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-bootstrap</artifactId>
        </dependency>
        <!-- support health check -->
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-web-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-iris-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-cache-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <!-- other dependencies -->
        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
            <version>1.1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.39</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>9.0.39</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>9.0.39</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <!-- other dependencies -->

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <!-- other dependencies -->
    </dependencies>
    <build>
        <finalName>tax-asynctask</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.4.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>