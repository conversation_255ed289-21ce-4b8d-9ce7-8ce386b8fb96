package cn.com.servyou.asynctask.main;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

@SpringBootApplication(exclude ={TransactionAutoConfiguration.class, PersistenceExceptionTranslationAutoConfiguration.class, DataSourceAutoConfiguration.class})
@ImportResource(locations = {
    "classpath:/spring/app-deploy/spring-load.xml",
    "classpath:/spring/customize/*.xml"
})
@ComponentScan({"cn.com.servyou.asynctask"})
@MapperScan("cn.com.servyou.asynctask.dao")
public class AppSampleBootApplication {

    public static void main(String[] args) {
        new SpringApplicationBuilder(AppSampleBootApplication.class).run(args);
    }

}