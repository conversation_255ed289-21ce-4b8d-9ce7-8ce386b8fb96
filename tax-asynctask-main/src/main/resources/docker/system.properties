########################################################################################
#                                  数据库配置                                            #
########################################################################################
dataSource.pt.mode=MAIN
dataSource.primary.driverClass=com.mysql.jdbc.Driver
dataSource.primary.jdbcUrl=***********************************************************************************************************************************************************
dataSource.primary.user=test_tax_asynctask@piaoshui_test
dataSource.primary.password=vault://decrypt?appCode=tax-asynctask&keyId=517eeccd-3a2b-45f8-9bda-d6400a3d8d88&cipherKey=k/EwzmrDzxyZAGuKnjzqHuoEKnVdfAsCKgwVtoj3rGk=&cipherText=am+bQxl87hNTKqoGDgUQodKdFADs05gXPpzZZzotWpc=
########################################################################################
#                                  redis参数配置                                        #
########################################################################################
redis.pt.mode=MAIN
redis.primary.sentinels=*************:8420;*************:8421;*************:8422
redis.primary.masterName=tax-asynctask-test-6420
#redis.primary.host=
#redis.primary.port=
redis.primary.password=vault://decrypt?appCode=tax-asynctask&keyId=beca15a4-6f17-4251-831b-98bee4376620&cipherKey=35flidplRthFtxBrSWae6hPuCZGlhK3T0IbJlw4+los=&cipherText=GEGJTJh7DD7jyrgqM6pbO2dYh3iZ9Bp3++RQSSAAym5HB0sYsjLc7o+r25vNPyGw
redis.primary.db=0
redis.primary.timeout=6000
redis.primary.maxactive=300
redis.primary.maxwait=6000
redis.primary.maxidle=300
redis.primary.minidle=100
#########################################################################################
#                                spring boot properties                                 #
#########################################################################################
log.dal.bean.names=*DAO,*Dao
# http服务启用端口
server.port=8080
spring.aop.proxy-target-class=true
########################################################################################
#                                  rocketmq参数配置                                     #
########################################################################################
rocketmq.producer.base-packages=cn.com.servyou.asynctask.shared.producer
rocketmq.producer.namesrv=**************:9876;10.199.150.169:9876
rocketmq.producer.group=asynctask-producer
rocketmq.producer.namespace=gufl
#rocketmq.producer.transaction-listener-name=transactionListener

#rocketmq.consumer.group=asynctask-test-consumer
#rocketmq.consumer.namesrv=**************:9876;10.199.150.169:9876
#rocketmq.consumer.namespace=gufl
#rocketmq.topic=gufl-topic
#rocketmq.tag=gufl-tag

rocketmq.producer.asynctask.schedule.topic=tax-asynctask-topic-schdule-notify
rocketmq.consumer2.group=asynctask-schedule-consumer-gufl
rocketmq.consumer2.namesrv=**************:9876;10.199.150.169:9876
rocketmq.consumer2.namespace=gufl

rocketmq.consumer.taxlineup.biznotify.topic=taxlineup-topic-biz-notify
rocketmq.consumer3.group=asynctask-biznotify-consumer-gufl
rocketmq.consumer3.namesrv=**************:9876;10.199.150.169:9876
rocketmq.consumer3.namespace=test
fd.queue.tag=SaasDeclare_queuedFor_gufl || LM_queuedFor_gufl
asynctask.lineup.tag.suffix=queuedFor_gufl
############################# 以下配置非本地环境中请引用公共配置 ##############################
# 增加这项配置，会激活http方式的vault解密
vault.service.url=http://jupiter-gateway.servyou-stable.sit.91lyd.com?accessKeyId=OUU5RTNFNEY1OTAwNEI5MDhFQTdCNjI2RkY3OTY4MDU=&accessKeySecret=N+Gkly7Rd0w+rTEPe/q6hA==
#增加这项配置，以获取governor上配置的sentinel限流规则
sentinel.dashboard.address=http://governor-ui.arch-test.sit.91lyd.com
#增加这项配置，会激活rocketmq的规则，如有顺序发送或是幂等消费等功能，需要配置此功能。详情请查看公共架构confluence文档
rocketmq.governor.url=http://governor-ui.arch-test.sit.91lyd.com
spring.profiles.active=local
