<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="app.name" value="tax-asynctask"/>
    <property name="logging.root" value="/usr/local/logs"/>
    <!-- 异步日志配置 -->
    <!--<property name="async.appender.switch" value="true" scope="context"/>-->
    <!--<property name="async.appender.queue.size" value="1000" scope="context"/>-->
    <!--<property name="async.appender.never.block" value="true" scope="context"/>-->
    <!--<property name="async.appender.discarding.threshold" value="500" scope="context"/>-->
    <!-- 控制台 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>%date [%-5level] [%thread] %logger{80} - %msg%X{traceId}%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR-APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-error.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date [%-5level] [%thread] %logger{80} - %msg%X{traceId}%n</pattern>
        </encoder>
    </appender>

    <appender name="SYS-WEB-DETAIL-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-web-detail.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-web-detail.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date [%X{loginUserID},%X{loginName},%X{remoteAddr},%X{method},%X{requestURIWithQueryString},%X{userAgent}] %msg%X{traceId}%n</pattern>
        </encoder>
    </appender>

    <logger name="SYS-WEB-DETAIL" additivity="false">
        <appender-ref ref="SYS-WEB-DETAIL-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="console"/>
    </logger>

    <appender name="WEB-ANALYTICS-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-web-analytics.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-web-analytics.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date %msg%X{traceId}%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="WEB-ANALYTICS" additivity="false">
        <appender-ref ref="WEB-ANALYTICS-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
        <appender-ref ref="console"/>
    </logger>

    <appender name="DEFAULT-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.root}/${app.name}/common-default.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.root}/${app.name}/common-default.log.%d{yyyy-MM-dd}</FileNamePattern>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>%date [%-5level] [%thread] %logger{80} - %msg%X{traceId}%n</pattern>
        </encoder>
    </appender>

    <!-- 任务调度链路开始 -->
    <appender name="SCHEDULE-APPENDER" class="ch.qos.logback.classic.sift.SiftingAppender">
        <timeout>365day</timeout>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <discriminator class="cn.com.servyou.i7boot.log.logback.PtDiscriminator">
            <key>destination</key>
            <logFileName>common-schedule</logFileName>
        </discriminator>
        <sift>
            <appender name="FILE-${destination}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logging.root}/${app.name}/${destination}.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <FileNamePattern>${logging.root}/${app.name}/${destination}.log.%d{yyyy-MM-dd}</FileNamePattern>
                    <MaxHistory>10</MaxHistory>
                </rollingPolicy>
                <encoder charset="UTF-8">
                    <pattern>%date%X{traceId}-%msg%n</pattern>
                </encoder>
            </appender>
        </sift>
    </appender>

    <logger name="SCHEDULE-DIGEST" additivity="false">
        <appender-ref ref="SCHEDULE-APPENDER"/>
    </logger>
    <!-- 任务调度链路开始 -->

    <springProfile name="local">
        <root level="DEBUG">
            <appender-ref ref="DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
            <appender-ref ref="console"/>
        </root>
        <logger name="MyBatis" additivity="false">
            <level value="DEBUG"/>
        </logger>
    </springProfile>
    <springProfile name="!local">
        <root level="INFO">
            <appender-ref ref="DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </root>
    </springProfile>

</configuration>