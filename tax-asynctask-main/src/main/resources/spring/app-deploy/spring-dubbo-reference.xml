<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="tax-asynctask" />

    <dubbo:reference registry="asynctask" group="${taxbase.dubbo.group}" version="${taxbase.dubbo.version}"
                     interface="cn.com.servyou.taxbase.facade.DeclarationFacade"
                     id="declarationFacade"
                     check="false" timeout="${taxbase.webActionFacade.dubbo.timeout:150000}" >
        <dubbo:method name="queryOrInit" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
        <dubbo:method name="excuteSyncStatusTask" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
        <dubbo:method name="selfCheck" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
    </dubbo:reference>

    <dubbo:reference registry="asynctask" group="${taxbase.dubbo.group}"
                     version="${taxbase.dubbo.version}"
                     interface="cn.com.servyou.taxbase.facade.TaxIdentificationFacade"
                     id="taxIdentificationFacade" timeout="${taxbase.taxIdentificationFacade.dubbo.timeout:150000}"
                     check="false"/>

    <dubbo:reference registry="asynctask" group="${taxgateway.dubbo.group}" version="${taxgateway.dubbo.version}"
                     interface="cn.com.servyou.xqy.gateway.facade.FdDeclareStateFacade"
                     id="fdDeclareStateFacade"
                     check="false" timeout="${taxbase.fdDeclareStateFacade.dubbo.timeout:90000}" >
        <dubbo:method name="querySfzDataNew" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
        <dubbo:method name="cutPictureForDsfWsb" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
        <dubbo:method name="tbcGetMergeResult" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
    </dubbo:reference>
    <!--登录 start-->
    <dubbo:reference registry="asynctask" group="${taxbase.dubbo.group}" version="${taxbase.dubbo.version}"
                     interface="cn.com.servyou.taxbase.facade.BatchDataTaskFacade"
                     id="batchDataTaskFacade"
                     check="false" timeout="${taxbase.batchDataTaskFacade.dubbo.timeout:90000}" >
        <dubbo:method name="excuteLoginTask" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
    </dubbo:reference>
    <!--登录 end-->

    <dubbo:reference registry="asynctask" group="${taxbase.dubbo.group}"
                     version="${taxbase.dubbo.version}"
                     interface="cn.com.servyou.taxbase.facade.AsynTaskCallbackFacade"
                     id="asynTaskCallbackFacade" timeout="${taxbase.asynTaskCallbackFacade.dubbo.timeout:150000}"
                     check="false"/>

    <!-- taxlineup -->
    <dubbo:reference registry="asynctask" group="${taxlineup.dubbo.group}"
                     version="${taxlineup.dubbo.version}"
                     interface="cn.com.servyou.taxlineup.facade.TaskTicketFacade"
                     id="taskTicketFacade" timeout="5000"
                     check="false"/>

    <!--taxcore-->
    <dubbo:reference registry="asynctask" group="${taxcore.dubbo.group}" version="${taxcore.dubbo.version}"
                     interface="cn.com.servyou.xqy.taxcore.facade.basic.taxpayer.TaxpayerFacade"
                     id="taxpayerFacade"
                     check="false" timeout="${taxcore.taxpayerFacade.dubbo.timeout:150000}" >
        <dubbo:method name="getTaxpayerByTaxNoAndAreaCodeCurrentMonthForTask" async="true" onreturn="notify.onreturn" onthrow="notify.onthrow"/>
    </dubbo:reference>


    <bean id="notify" class="cn.com.servyou.asynctask.shared.impl.Callback"/>


</beans>