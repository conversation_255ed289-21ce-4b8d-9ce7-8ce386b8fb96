<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
       default-lazy-init="true">

    <dubbo:registry  id="asynctask" protocol="zookeeper" address="${dubbo.address}" />
    <dubbo:protocol name="dubbo" id="DUBBOInnerService" port="${dubbo.port}" payload="${dubbo.payload:8388608}"/>
    <dubbo:provider protocol="DUBBOInnerService" filter="appNameDubboProviderFilter" timeout="3000" retries="0" export="${dubbo.isExportService:true}"
                    threadpool="${dubbo.provider.threadpool:fixed}" threads="${dubbo.provider.threads:200}"/>
    <dubbo:service  group="${dubbo.group}" version="${dubbo.version}"
                    interface="cn.com.servyou.asynctask.facade.TestFacade" ref="testFacadeImpl" timeout="1000000"/>
    <dubbo:service  group="${dubbo.group}" version="${dubbo.version}"
                    interface="cn.com.servyou.asynctask.facade.TaskFacade" ref="taskFacadeImpl" timeout="30000"/>

</beans>