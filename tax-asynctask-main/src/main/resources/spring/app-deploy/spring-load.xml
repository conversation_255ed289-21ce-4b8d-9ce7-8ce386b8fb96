<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

        <!-- DUBBO -->
    <import resource="classpath:spring/app-deploy/spring-dubbo-service.xml"/>
    <import resource="classpath:spring/app-deploy/spring-dubbo-reference.xml"/>
    <import resource="classpath:spring/framework-redisson/spring-redisson.xml"/>

    <bean id = "declarationFacadeClient" class="cn.com.servyou.asynctask.integration.impl.DeclarationFacadeClientImpl"/>
    <bean id = "fdDeclareStateFacadeClient" class="cn.com.servyou.asynctask.integration.impl.FdDeclareStateFacadeClientImpl"/>
    <bean id = "taxpayerFacadeClient" class="cn.com.servyou.asynctask.integration.impl.TaxpayerFacadeClientImpl"/>
    <bean id = "taxIdentificationFacadeClient" class="cn.com.servyou.asynctask.integration.impl.TaxIdentificationFacadeClientImpl"/>
    <bean id = "asynTaskCallbackFacadeClient" class="cn.com.servyou.asynctask.integration.impl.AsynTaskCallbackFacadeClientImpl"/>
    <bean id = "batchDataTaskFacadeClient" class="cn.com.servyou.asynctask.integration.impl.BatchDataTaskFacadeClientImpl"/>
</beans>