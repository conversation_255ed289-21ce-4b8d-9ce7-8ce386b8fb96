package cn.com.servyou.asynctask.test;

import cn.com.servyou.asynctask.core.service.TestService;
import cn.com.servyou.asynctask.main.AppSampleBootApplication;
import cn.com.servyou.i7boot.test.boot.BootstrapExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = AppSampleBootApplication.class)
@ExtendWith(BootstrapExtension.class)
public class TestServiceTest {

    @Autowired
    private TestService service;

    @Test
    public void test() {
        System.out.println(service.get());
    }
}