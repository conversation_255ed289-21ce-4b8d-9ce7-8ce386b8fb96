package cn.com.servyou.asynctask.msg.consumer;

import cn.com.servyou.i7boot.mq.spring.annotation.EnableRocketMQConsumer;
import cn.com.servyou.i7boot.mq.spring.annotation.EnableRocketMQConsumers;
import org.springframework.context.annotation.Configuration;

/**
 * rocketmq 消费者配置项
 * 每一个 {@link EnableRocketMQConsumer#prefix()} 代表一个消费者组，包含{@link EnableRocketMQConsumer#beans()}下的所有消费类，
 * 通过{@link EnableRocketMQConsumer#prefix()}获取对应配置组的前缀名
 */
@EnableRocketMQConsumers({
//        @EnableRocketMQConsumer(beans = RocketMQTestConsumer.class, prefix = "rocketmq.consumer"),
        @EnableRocketMQConsumer(beans = {TaskScheduleConsumer.class, TaskIntimeScheduleConsumer.class}, prefix = "rocketmq.consumer2"),
        @EnableRocketMQConsumer(beans = TaskTicketConsumer.class, prefix = "rocketmq.consumer3")
})
@Configuration
public class MQConsumerConfiguration {

}
