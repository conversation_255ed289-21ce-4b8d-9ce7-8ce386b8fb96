package cn.com.servyou.asynctask.msg.consumer;

import com.servyou.elephant.mq.Body;
import com.servyou.elephant.mq.ConsumerConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * RocketMQ 消费者示例
 */
@Slf4j
public class RocketMQTestConsumer {

    @ConsumerConfig(topic = "${rocketmq.topic}", tag = "${rocketmq.tag}")
    public void onMessage(@Body String message) {
        log.info("RocketMQTestConsumer消费：{}" , message);
    }
}
