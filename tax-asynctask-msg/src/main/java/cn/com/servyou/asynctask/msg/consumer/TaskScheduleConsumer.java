package cn.com.servyou.asynctask.msg.consumer;

import cn.com.servyou.asynctask.core.domain.AsyncTaskScheduleMsgBody;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.com.servyou.asynctask.shared.AsyncTaskScheduleManagement;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.servyou.elephant.mq.Acknowledge;
import com.servyou.elephant.mq.Body;
import com.servyou.elephant.mq.ConsumerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/17 20:03
 */
@Slf4j
public class TaskScheduleConsumer {
    @Autowired
    private AsyncTaskScheduleManagement asyncTaskScheduleManagement;

    @ConsumerConfig(topic = "${rocketmq.producer.asynctask.schedule.topic}")
    public Acknowledge onMessage(@Body String message) {
        log.info("TaskScheduleConsumer消费：{}", message);
        if (StrUtil.isEmpty(message) || !JSONUtil.isTypeJSON(message)) {
            log.warn("[任务调度MQ消费]消息内容为空或格式错误1，msg={}", message);
            return Acknowledge.COMMIT;
        }
        AsyncTaskScheduleMsgBody asyncTaskScheduleMsgBody = JSONUtil.toBean(message, AsyncTaskScheduleMsgBody.class);
        if (null == asyncTaskScheduleMsgBody || StrUtil.isEmpty(asyncTaskScheduleMsgBody.getCustomerId()) || null == asyncTaskScheduleMsgBody.getBizTaskId()) {
            log.warn("[任务调度MQ消费]消息内容为空或格式错误2，msg={}", message);
            return Acknowledge.COMMIT;
        }
        asyncTaskScheduleManagement.execSchedule(asyncTaskScheduleMsgBody);
        return Acknowledge.COMMIT;

    }
}
