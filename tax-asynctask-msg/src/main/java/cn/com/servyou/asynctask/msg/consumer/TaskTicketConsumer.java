package cn.com.servyou.asynctask.msg.consumer;

import cn.com.servyou.asynctask.core.domain.AsyncTaskBaseDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskScheduleDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskUpdateAndRecordLogDTO;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.asynctask.core.service.BizTaskService;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.shared.AsyncTaskScheduleManagement;
import cn.com.servyou.asynctask.shared.TaskInvokeManagement;
import cn.com.servyou.taxlineup.facade.dto.TaskMsgBodyDTO;
import cn.com.servyou.taxlineup.facade.enums.MsgCodeEnum;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.servyou.elephant.mq.Acknowledge;
import com.servyou.elephant.mq.Body;
import com.servyou.elephant.mq.ConsumerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 排队结果消费者
 * @Date 2024/8/19
 */
@Slf4j
public class TaskTicketConsumer {
    @Autowired
    private AsyncTaskScheduleManagement asyncTaskScheduleManagement;
    @Autowired
    private TaskInvokeManagement taskInvokeManagement;
    @Autowired
    private BizTaskService bizTaskService;

    @ConsumerConfig(topic = "${rocketmq.consumer.taxlineup.biznotify.topic}", tag = "${fd.queue.tag}")
    public Acknowledge onMessage(@Body String message) {
        log.info("TaskTicketConsumer消费排队结果：{}", message);
        if (StrUtil.isEmpty(message) || !JSONUtil.isTypeJSON(message)) {
            log.warn("[排队结果MQ消费]消息内容为空或格式错误，msg={}", message);
            return Acknowledge.COMMIT;
        }

        try {
            handleTaskTicket(message);
        } catch (Exception ex) {
            log.error("排队结果MQ消费失败", ex);
        } finally {
            return Acknowledge.COMMIT;
        }

    }

    private void handleTaskTicket(String msg) {

        TaskMsgBodyDTO lineUpResult = JSON.parseObject(msg, TaskMsgBodyDTO.class);
        String code = lineUpResult.getCode();

        log.info("TaskTicketConsumer消费排队结果：code={}", code);

        String businessData = lineUpResult.getBusinessData();
        if (StrUtil.isEmpty(businessData) || !JSONUtil.isTypeJSON(businessData)) {
            log.info("TaskTicketConsumer消费排队透传信息不全：businessData={}", businessData);
            return;
        }
        JSONObject jsonObject = JSONUtil.parseObj(businessData);
        String customerId = jsonObject.getStr("customerId");
        Long taskId = jsonObject.getLong("taskId");
        if (StrUtil.isEmpty(customerId) || null == taskId) {
            log.info("TaskTicketConsumer消费排队透传信息不全：businessData={}", businessData);
            return;
        }

        ScheduleLogUtils.log("排队MQ消费", taskId, customerId, "lineUpResult={}", JSONUtil.toJsonStr(lineUpResult));

        if (Lists.newArrayList(MsgCodeEnum.C000001.getCode(), MsgCodeEnum.C000003.getCode(),
                MsgCodeEnum.C000004.getCode()).contains(code)) {
            // 不处理。由【业务执行结果】处理
            ScheduleLogUtils.log("排队MQ消费", taskId, customerId, "不是排队超时或排队成功，不处理");
            return;
        }

        BizTaskDO bizTaskDO = bizTaskService.getTaskById(new AsyncTaskBaseDTO(customerId, taskId));

        if (StrUtil.equals(MsgCodeEnum.C000000.getCode(), code)) {
            bizTaskDO.setLineupTicket(lineUpResult.getTicket());
            bizTaskService.updateTaskTicket(bizTaskDO);
            ScheduleLogUtils.log("排队MQ消费", taskId, customerId, "更新ticket记录");
            taskInvokeManagement.doBusinessRequest(bizTaskDO);
            return;
        }

        if (StrUtil.equals(MsgCodeEnum.C000002.getCode(), code)) {
            // 更新状态并记录日志
            AsyncTaskUpdateAndRecordLogDTO recordLogDTO = new AsyncTaskUpdateAndRecordLogDTO();
            recordLogDTO.setBizTaskId(taskId);
            recordLogDTO.setCustomerId(customerId);
            recordLogDTO.setTaskStatus(TaskStatusEnum.FAIL);
            recordLogDTO.setReason("排队超时,lineupTaskId=" + lineUpResult.getTaskId());
            recordLogDTO.setStartTime(new Date());
            recordLogDTO.setEndTime(new Date());
            recordLogDTO.setBizPartCode(bizTaskDO.getBizPartCode());
            recordLogDTO.setBizCode(bizTaskDO.getBizCode());
            asyncTaskScheduleManagement.recordLogAndUpdateStatus(recordLogDTO);
            bizTaskDO = bizTaskService.getTaskById(new AsyncTaskBaseDTO(customerId, taskId));
            AsyncTaskScheduleDTO asyncTaskScheduleDTO = new AsyncTaskScheduleDTO(bizTaskDO, Boolean.FALSE);
            asyncTaskScheduleDTO.setLineupAPIError(true);
            asyncTaskScheduleDTO.setErrorCode(lineUpResult.getCode());
            asyncTaskScheduleDTO.setErrorMessage(lineUpResult.getMessage());
            asyncTaskScheduleManagement.schedule(asyncTaskScheduleDTO);
        }

    }
}
