<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.com.servyou</groupId>
        <artifactId>tax-asynctask</artifactId>
        <version>${version.number}-${build.number}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tax-asynctask-shared</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-mq-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-log</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
