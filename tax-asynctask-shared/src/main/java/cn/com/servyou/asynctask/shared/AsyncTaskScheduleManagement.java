package cn.com.servyou.asynctask.shared;

import cn.com.servyou.asynctask.core.domain.*;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.facade.dto.TaskReqDTO;
import cn.com.servyou.asynctask.facade.dto.TaskRespDTO;

/**
 * @Description 异步任务调度
 * <AUTHOR>
 * @Date 2024/8/15 17:02
 */
public interface AsyncTaskScheduleManagement {

    /**
     * 任务合并
     *
     * @param applyBizTaskDO 当前申请的任务DO
     * @param bizTaskConfDO  配置
     * @return
     */
    AsyncTaskMergeDTO merge(BizTaskDO applyBizTaskDO, BizTaskConfDO bizTaskConfDO);

    /**
     * 任务调度
     *
     * @param asyncTaskScheduleDTO
     */
    void schedule(AsyncTaskScheduleDTO asyncTaskScheduleDTO);

    /**
     * MQ执行任务调度
     *
     * @param asyncTaskScheduleMsgBody
     */
    void execSchedule(AsyncTaskScheduleMsgBody asyncTaskScheduleMsgBody);

    /**
     * 是否达到可重试次数
     * @param bizTaskDO
     * @return
     */
    boolean isExecLimit(BizTaskDO bizTaskDO);

    /**
     * 是否是未排队异常
     *
     * @return
     */
    Boolean isUnQueuedExc(String errorCode);

    /**
     * 是否是可重试异常
     *
     * @return
     */
    Boolean isRetryExc(AsyncTaskCheckRetryExcDTO asyncTaskCheckRetryExcDTO);

    /**
     * 记录日志并更新状态
     */
    void recordLogAndUpdateStatus(AsyncTaskUpdateAndRecordLogDTO recordLogDTO);

    /**
     * 创建任务
     * @param taskReqDTO
     * @return
     */
    TaskRespDTO createTask(TaskReqDTO taskReqDTO);

    /**
     * 任务未排队执行计数
     * @return
     */
    int addUnQueueExecutedNum(BizTaskDO bizTaskDO);

    /**
     * 是否可未排队执行
     * @return
     */
    boolean isUnqueueExcAble(Integer currentUnQueueExecuteNum);

}
