package cn.com.servyou.asynctask.shared;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskConfigExtendDTO;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/11 18:57
 */
public interface BizResultNotifyManagement {
    ResultNotifyRespDTO resultNotify(ResultNotifyReqDTO resultNotifyReqDTO, AsyncTaskConfigExtendDTO asyncTaskConfigExtendDTO);
}
