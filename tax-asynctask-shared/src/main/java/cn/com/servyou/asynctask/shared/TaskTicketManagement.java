package cn.com.servyou.asynctask.shared;

import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.taxlineup.facade.dto.InLineReqDTO;
import cn.com.servyou.taxlineup.facade.dto.InLineRespDTO;

/**
 * <AUTHOR>
 * @description: TaskTicketManagement interface
 * @since 2024/8/19
 */
public interface TaskTicketManagement {

    /**
     * 申请排队
     * @param bizTaskDO
     * @return
     */
    InLineRespDTO getTicket(BizTaskDO bizTaskDO);

    /**
     * 释放排队
     */
    void releaseTicket(String ticket);
}
