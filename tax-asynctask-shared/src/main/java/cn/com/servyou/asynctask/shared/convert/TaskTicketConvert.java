package cn.com.servyou.asynctask.shared.convert;

import cn.com.servyou.asynctask.core.config.TaskConfig;
import cn.com.servyou.asynctask.core.domain.AsyncTaskExtendDTO;
import cn.com.servyou.asynctask.core.utils.SpringBeanUtils;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.taxlineup.facade.dto.InLineReqDTO;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description:
 * @since 2024/8/19
 */
public class TaskTicketConvert {

    public static InLineReqDTO toInLineReqDTO(BizTaskDO bizTaskDO) {
        if (bizTaskDO == null) {
            return null;
        }

        InLineReqDTO inLineReqDTO = new InLineReqDTO();

        inLineReqDTO.setTaxNo(bizTaskDO.getTaxNo());
        inLineReqDTO.setCustomerId(bizTaskDO.getCustomerId());
        inLineReqDTO.setAccountType(bizTaskDO.getAccountType());
        inLineReqDTO.setBizType(bizTaskDO.getBusinessType());
        inLineReqDTO.setAccountId(bizTaskDO.getAccountId());
        inLineReqDTO.setAreaCode(bizTaskDO.getAreaCode());
        inLineReqDTO.setBizCode(bizTaskDO.getBizCode());
        inLineReqDTO.setBizPartCode(bizTaskDO.getBizPartCode());
        TaskConfig taskConfig = SpringBeanUtils.getBean(TaskConfig.class);
        inLineReqDTO.setBizMqTag(taskConfig.getLineupTagSuffix());
        if (StrUtil.isNotBlank(bizTaskDO.getExtend())){
            AsyncTaskExtendDTO asyncTaskExtendDTO = JSON.parseObject(bizTaskDO.getExtend(), AsyncTaskExtendDTO.class);
            inLineReqDTO.setExtData(Optional.ofNullable(asyncTaskExtendDTO).map(AsyncTaskExtendDTO::getExtData).orElse(null));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("taskId", bizTaskDO.getId());
        jsonObject.put("customerId", bizTaskDO.getCustomerId());
        inLineReqDTO.setBusinessData(JSON.toJSONString(jsonObject));
        return inLineReqDTO;
    }
}
