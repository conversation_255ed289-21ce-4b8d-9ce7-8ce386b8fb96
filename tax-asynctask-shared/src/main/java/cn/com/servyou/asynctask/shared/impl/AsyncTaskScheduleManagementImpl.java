package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.core.common.Mvel2ScriptService;
import cn.com.servyou.asynctask.core.common.TransactionService;
import cn.com.servyou.asynctask.core.config.Pair;
import cn.com.servyou.asynctask.core.config.TaskConfig;
import cn.com.servyou.asynctask.core.domain.*;
import cn.com.servyou.asynctask.core.enums.EndFlagEnum;
import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.asynctask.core.enums.ExecTypeEnum;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.asynctask.core.service.BizTaskLogService;
import cn.com.servyou.asynctask.core.service.BizTaskService;
import cn.com.servyou.asynctask.core.service.RedisService;
import cn.com.servyou.asynctask.core.utils.MD5Util;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.facade.dto.TaskReqDTO;
import cn.com.servyou.asynctask.facade.dto.TaskRespDTO;
import cn.com.servyou.asynctask.shared.*;
import cn.com.servyou.asynctask.shared.producer.TaskScheduleProducer;
import cn.com.servyou.i7boot.trace.TraceConstants;
import cn.com.servyou.i7boot.trace.TraceUtils;
import cn.com.servyou.taxlineup.facade.dto.InLineRespDTO;
import cn.com.servyou.xqy.framework.core.TraceTags;
import cn.com.servyou.xqy.framework.exception.BusinessException;
import cn.com.servyou.xqy.gateway.constant.AreacodeEnum;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.com.servyou.asynctask.core.config.TaskConfig.decodeMergeVal;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/15 17:02
 */
@Slf4j
@Service
public class AsyncTaskScheduleManagementImpl implements AsyncTaskScheduleManagement {

    @Autowired
    private RedisService redisService;
    @Autowired
    private BizTaskService bizTaskService;
    @Autowired
    private BizTaskLogService bizTaskLogService;
    @Autowired
    private TaskScheduleProducer taskScheduleProducer;
    @Autowired
    private TaskConfig taskConfig;
    @Autowired
    private Mvel2ScriptService mvel2ScriptService;
    @Autowired
    private TaskInvokeManagement taskInvokeManagement;
    @Autowired
    private TaskTicketManagement taskTicketManagement;
    @Autowired
    private BizTaskConfManagement bizTaskConfManagement;
    @Autowired
    private TransactionService transactionService;
    @Autowired
    private BizResultNotifyManagement bizResultNotifyManagement;


    /**
     * 第一版可以不考虑锁
     * 根据配置规则中的入参，计算MD5作为缓存key，bizTaskId为value，缓存时间为600s(配置)，新的任务进来，计算的MD5取缓存，无缓存则执行后续步骤，有缓存则取消缓存中bizTaskId任务,
     * 更新缓存中bizTaskId
     *
     * @param applyBizTaskDO 当前申请的任务DO
     * @return
     */
    @Override
    public AsyncTaskMergeDTO merge(BizTaskDO applyBizTaskDO, BizTaskConfDO bizTaskConfDO) {
        ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                "bizTaskConfDO={}", JSONUtil.toJsonStr(bizTaskConfDO));
        if (StrUtil.startWith(applyBizTaskDO.getExecuteRuleContent(), "0")) {
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }
        if (StrUtil.isBlank(bizTaskConfDO.getMergeTimeRange()) || StrUtil.isBlank(bizTaskConfDO.getMergeRuleContent())){
            // 未配置合并规则，不执行合并
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }
        String cacheKey = getCacheKey(bizTaskConfDO, applyBizTaskDO);
        if (StrUtil.isEmpty(cacheKey)) {
            // md5为空，不合并
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(), "cacheKey，不合并");
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }
        // 合并时间范围
        Long mergeTimeRande = getMergeTimeRangeByAreaCode(bizTaskConfDO.getMergeTimeRange(), applyBizTaskDO.getAreaCode());
        String value = redisService.get(cacheKey);
        if (StrUtil.isEmpty(value)) {
            redisService.set(cacheKey, TaskConfig.buildMergeVal(applyBizTaskDO.getId(),
                            applyBizTaskDO.getCustomerId()), mergeTimeRande,
                    TimeUnit.SECONDS);
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                    "缓存不存在，不合并，当前任务写入缓存");
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }
        Pair<Long, String> pair = decodeMergeVal(value);
        // 如果上一个任务执行中，则合并舍弃当前任务
        BizTaskDO lastTaskDO = bizTaskService.getTaskById(new AsyncTaskBaseDTO(pair.getValue(), pair.getKey()));
        if (null == lastTaskDO){
            redisService.set(cacheKey, TaskConfig.buildMergeVal(applyBizTaskDO.getId(),
                            applyBizTaskDO.getCustomerId()), mergeTimeRande,
                    TimeUnit.SECONDS);
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                    "任务不存在，不合并，当前任务写入缓存");
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }
        if (EndFlagEnum.END.getCode().equals(lastTaskDO.getEndFlag())) {
            // 覆盖缓存
            redisService.set(cacheKey, TaskConfig.buildMergeVal(applyBizTaskDO.getId(),
                            applyBizTaskDO.getCustomerId()), mergeTimeRande,
                    TimeUnit.SECONDS);
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                    "缓存任务已结束，覆盖缓存，不合并，当前任务写入缓存");
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }

        if (TaskStatusEnum.NOT_EXECUTE.getCode().equals(lastTaskDO.getStatus())) {
            // 取消上一个任务
            bizTaskService.endTask(AsyncTaskEndDTO.endForMerge(pair.getKey(), pair.getValue(), applyBizTaskDO.getId()));
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                    "缓存存在，合并，取消上一个任务{}", pair.getKey());
            // 更新缓存
            redisService.setIfPresent(cacheKey, TaskConfig.buildMergeVal(applyBizTaskDO.getId(),
                    applyBizTaskDO.getCustomerId()));
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                    "缓存存在，合并，更新缓存");
            return new AsyncTaskMergeDTO(Boolean.TRUE, pair.getKey(), applyBizTaskDO.getId());

        } else {
            // 任务已调度，覆盖缓存
            redisService.set(cacheKey, TaskConfig.buildMergeVal(applyBizTaskDO.getId(),
                    applyBizTaskDO.getCustomerId()), mergeTimeRande, TimeUnit.SECONDS);
            ScheduleLogUtils.log("创建任务-合并任务", applyBizTaskDO.getId(), applyBizTaskDO.getCustomerId(),
                    "缓存任务已调度，覆盖缓存，不合并，当前任务写入缓存");
            return new AsyncTaskMergeDTO(Boolean.TRUE, null, applyBizTaskDO.getId());
        }

    }


    /**
     * 根据current_exec_num，exec_rule，end_mark判断当前是否可执行，若可执行，取对应的延时时间，并更新current_exec_num+1。
     * 发送【任务调度延时MQ】（延时MQ所支持的最小延迟为1s，这意味着不存在立即执行的任务，最快延时1s）
     * 若不可执行，更新任务并返回任务结束
     * 如果是未排队异常，直接【申请排队】
     *
     * @param asyncTaskScheduleDTO
     */
    public void schedule(AsyncTaskScheduleDTO asyncTaskScheduleDTO) {
        this.transactionService.execute(transactionStatus -> {
            BizTaskDO bizTaskDO = asyncTaskScheduleDTO.getBizTaskDO();
            ScheduleLogUtils.log("任务调度", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "第{}次触发任务调度",
                    bizTaskDO.getExecuteNum() + 1);
            if (EndFlagEnum.END.getCode().equals(bizTaskDO.getEndFlag())) {
                // 任务已结束
                ScheduleLogUtils.log("任务调度", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "任务已结束");
                return 1;
            }
            if (!ExecTypeEnum.QUEUED_EXEC.getCode().equals(bizTaskDO.getExecType()) && asyncTaskScheduleDTO.getIsUnQueuedExc()) {
                // 20250327 未排队重试次数增加限制 【https://zentao.dc.servyou-it.com/pro/task-view-778207.html】
                int currentUnQueueExecuteNum = this.addUnQueueExecutedNum(bizTaskDO);
                if (isUnqueueExcAble(currentUnQueueExecuteNum)) {
                    this.applyLineup(bizTaskDO);
                    ScheduleLogUtils.log("任务调度", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "任务未排队异常，直接排队，不增加重试次数");
                    return 2;
                } else {
                    ScheduleLogUtils.log("任务调度", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "任务未排队异常，但未排队可执行次数已上限，继续走正常调度逻辑");
                }

            }
            // 计算当前是否有可执行次数
            if (isExecLimit(bizTaskDO)) {
                bizTaskService.endTask(AsyncTaskEndDTO.endForExecLimit(bizTaskDO.getId(), bizTaskDO.getCustomerId()));
                ScheduleLogUtils.log("任务调度", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "可执行次数不足，结束任务,任务执行规则{},当前已执行{}，不可被调度", bizTaskDO.getExecuteRuleContent(), bizTaskDO.getExecuteNum());
                if (asyncTaskScheduleDTO.getNeedNotifyResult()) {
                    // 增加任务结束的回调
                    BizTaskConfDO conf = bizTaskConfManagement.getBizTaskConfByPartAndBizCodeOrTaskCode(bizTaskDO.getBizPartCode(), bizTaskDO.getBizCode(), bizTaskDO.getTaskCode());
                    AsyncTaskConfigExtendDTO configExtendDTO = Optional.ofNullable(conf).map(BizTaskConfDO::getExtend).map(AsyncTaskConfigExtendDTO::toDTO).orElse(null);
                    if (configExtendDTO != null && configExtendDTO.isNotifyAble()) {
                        ResultNotifyReqDTO resultNotifyReqDTO = new ResultNotifyReqDTO();
                        resultNotifyReqDTO.setAsyncTaskId(bizTaskDO.getId());
                        resultNotifyReqDTO.setSuccess(false);
                        resultNotifyReqDTO.setBizParams(bizTaskDO.getParameters());
                        if (StrUtil.isNotBlank(asyncTaskScheduleDTO.getErrorCode())) {
                            resultNotifyReqDTO.setErrorCode(asyncTaskScheduleDTO.getErrorCode());
                        }
                        if (StrUtil.isNotBlank(asyncTaskScheduleDTO.getErrorMessage())) {
                            resultNotifyReqDTO.setMessage(asyncTaskScheduleDTO.getErrorMessage());
                        }
                        resultNotifyReqDTO.setRetryAble(false);
                        resultNotifyReqDTO.setTaskCode(bizTaskDO.getTaskCode());
                        if (asyncTaskScheduleDTO.getLineupAPIError()) {
                            resultNotifyReqDTO.setLineupAPIError(true);
                        }
                        resultNotifyReqDTO.setBizPartCode(bizTaskDO.getBizPartCode());
                        resultNotifyReqDTO.setBizCode(bizTaskDO.getBizCode());
                        log.info("resultNotifyReqDTO={}", JSON.toJSONString(resultNotifyReqDTO));
                        ResultNotifyRespDTO resultNotifyRespDTO = this.bizResultNotifyManagement.resultNotify(resultNotifyReqDTO, configExtendDTO);
                        ScheduleLogUtils.log("任务执行结果通知-发送", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "resultNotifyReqDTO.success=false, resultNotifyRespDTO={}", JSON.toJSONString(resultNotifyRespDTO));
                    }
                }
                return 3;
            }
            // 获取当前执行的延时时间
            int delayTime =
                    Integer.parseInt(StrUtil.split(bizTaskDO.getExecuteRuleContent(), ",").get(bizTaskDO.getExecuteNum()));
            // 发送MQ
            AsyncTaskScheduleMsgBody msgBody = new AsyncTaskScheduleMsgBody();
            msgBody.setBizTaskId(bizTaskDO.getId());
            msgBody.setCustomerId(bizTaskDO.getCustomerId());
            if (0 == delayTime) {
                taskScheduleProducer.send(JSONUtil.toJsonStr(msgBody), String.valueOf(msgBody.getBizTaskId()));
            } else {
                taskScheduleProducer.send(JSONUtil.toJsonStr(msgBody), String.valueOf(msgBody.getBizTaskId()),
                        delayTime);
            }
            log.info("[任务调度]{}发送调度延时MQ,延时{}s", bizTaskDO.getId(), delayTime);
            ScheduleLogUtils.log("任务调度", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "发送调度延时MQ,延时{}s", delayTime);
//            }
            return 4;
        });
    }

    /**
     * 是否是终态end_mark，终态直接结束。
     * 如果current_exec_num == 第一次  且  exec_type == 预执行
     * 则触发【执行业务】
     * 否则【申请排队】
     *
     * @param asyncTaskScheduleMsgBody
     */
    @Override
    public void execSchedule(AsyncTaskScheduleMsgBody asyncTaskScheduleMsgBody) {
        ScheduleLogUtils.log("任务调度MQ消费", asyncTaskScheduleMsgBody.getBizTaskId(),
                asyncTaskScheduleMsgBody.getCustomerId(), "[execSchedule]asyncTaskScheduleMsgBody={}",
                JSONUtil.toJsonStr(asyncTaskScheduleMsgBody));
        // 查数据库任务
        BizTaskDO bizTaskDO = bizTaskService.getTaskById(asyncTaskScheduleMsgBody);
        if (null == bizTaskDO) {
            log.info("[任务调度MQ消费]当前任务不存在，MsgBody={}", JSONUtil.toJsonStr(asyncTaskScheduleMsgBody));
            ScheduleLogUtils.log("任务调度MQ消费", asyncTaskScheduleMsgBody.getBizTaskId(),
                    asyncTaskScheduleMsgBody.getCustomerId(), "[execSchedule]当前任务不存在");
            return;
        }
        // 判断是否是终态
        if (EndFlagEnum.END.getCode().equals(bizTaskDO.getEndFlag())) {
            log.info("[任务调度MQ消费]当前任务是终态，MsgBody={}", JSONUtil.toJsonStr(bizTaskDO));
            ScheduleLogUtils.log("任务调度MQ消费", asyncTaskScheduleMsgBody.getBizTaskId(),
                    asyncTaskScheduleMsgBody.getCustomerId(), "[execSchedule]当前任务是终态");
            return;
        }
        // 计算当前是否有可执行次数
        if (isExecLimit(bizTaskDO)) {
            bizTaskService.endTask(AsyncTaskEndDTO.endForExecLimit(bizTaskDO.getId(), bizTaskDO.getCustomerId()));
            ScheduleLogUtils.log("任务调度MQ消费", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "[execSchedule" +
                            "]可执行次数不足，结束任务,任务执行规则{},当前已执行{}，不可被调度",
                    bizTaskDO.getExecuteRuleContent(), bizTaskDO.getExecuteNum());
            return;
        }

        // 更新任务
        AsyncTaskUpdateDTO asyncTaskUpdateDTO = AsyncTaskUpdateDTO.buildForScheduleTask(bizTaskDO.getId(),
                bizTaskDO.getCustomerId(), bizTaskDO.getExecuteNum());
        Boolean result = bizTaskService.scheduleTask(asyncTaskUpdateDTO);
        ScheduleLogUtils.log("任务调度MQ消费", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "更新任务{}," +
                        "更新前执行次数{}，asyncTaskUpdateDTO={}", result, bizTaskDO.getExecuteNum(),
                JSONUtil.toJsonStr(asyncTaskUpdateDTO));
        if (result) {
            bizTaskDO = bizTaskService.getTaskById(asyncTaskScheduleMsgBody);
            BizTaskConfDO bizTaskConfDO =
                    bizTaskConfManagement.getBizTaskConfByPartAndBizCodeOrTaskCode(bizTaskDO.getBizPartCode(),
                            bizTaskDO.getBizCode(), bizTaskDO.getTaskCode());
            if (!StrUtil.startWith(bizTaskDO.getExecuteRuleContent(), "0")) {
                if (StrUtil.isNotBlank(bizTaskConfDO.getMergeTimeRange()) && StrUtil.isNotBlank(bizTaskConfDO.getMergeRuleContent())){
                    String cacheKey = getCacheKey(bizTaskConfDO, bizTaskDO);
                    String value = TaskConfig.buildMergeVal(bizTaskDO.getId(), bizTaskDO.getCustomerId());
                    redisService.delByCacheKeyIfValuePresent(cacheKey, value);
                    ScheduleLogUtils.log("任务调度MQ消费", asyncTaskScheduleMsgBody.getBizTaskId(),
                            asyncTaskScheduleMsgBody.getCustomerId(), "[execSchedule]任务调度，清除merge缓存，cacheKey={}，value={}",
                            cacheKey, value);
                }
            }
            // 判断是否是第一次执行
            if (bizTaskDO.getExecuteNum() == 0 && ExecTypeEnum.PRE_EXEC.getCode().equals(bizTaskDO.getExecType())) {
                ScheduleLogUtils.log("任务调度MQ消费", asyncTaskScheduleMsgBody.getBizTaskId(),
                        asyncTaskScheduleMsgBody.getCustomerId(), "[execSchedule]当前任务第一次执行且预执行模式");
                taskInvokeManagement.doBusinessRequest(bizTaskDO);
            } else if (ExecTypeEnum.QUEUED_EXEC.getCode().equals(bizTaskDO.getExecType())) {
                ScheduleLogUtils.log("任务调度MQ消费", asyncTaskScheduleMsgBody.getBizTaskId(),
                        asyncTaskScheduleMsgBody.getCustomerId(), "[execSchedule]当前任务已排队直接执行模式");
                taskInvokeManagement.doBusinessRequest(bizTaskDO);
            } else {
                // 申请排队
                this.applyLineup(bizTaskDO);
            }
        }

    }

    @Override
    public boolean isExecLimit(BizTaskDO bizTaskDO) {
        // 计算当前是否有可执行次数
        int currentExecNum = bizTaskDO.getExecuteNum();
        String execRule = bizTaskDO.getExecuteRuleContent();
        List<String> execRuleList = StrUtil.split(execRule, ",");
        return currentExecNum >= execRuleList.size();
    }

    private void applyLineup(BizTaskDO bizTaskDO) {
        try {
            ScheduleLogUtils.log("任务调度MQ消费", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "开始申请排队");
            InLineRespDTO inLineRespDTO = taskTicketManagement.getTicket(bizTaskDO);
            ScheduleLogUtils.log("任务调度MQ消费", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "申请排队成功,inLineRespDTO={}",
                    JSONUtil.toJsonStr(inLineRespDTO));
        } catch (Exception e) {
            log.error("[任务调度MQ消费]申请排队失败，bizTaskDO={}", JSONUtil.toJsonStr(bizTaskDO), e);
            ScheduleLogUtils.log("任务调度MQ消费", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "申请排队失败");
            String reason = "[申请排队异常]";
            AsyncTaskScheduleDTO asyncTaskScheduleDTO = new AsyncTaskScheduleDTO();
            if (e instanceof BusinessException) {
                BusinessException businessException = (BusinessException) e;
                reason += "CODE:" + businessException.getCode() + ",MESSAGE:" + businessException.getMessage();
                asyncTaskScheduleDTO.setErrorCode(businessException.getCode());
                asyncTaskScheduleDTO.setErrorMessage(businessException.getMessage());
            } else {
                reason += e.getMessage();
                asyncTaskScheduleDTO.setErrorCode(ErrorCodeEnum.INVALID_PARAM_ERROR.getCode());
                asyncTaskScheduleDTO.setErrorMessage(StrUtil.sub(e.getMessage(), 0,100));
            }
            AsyncTaskUpdateAndRecordLogDTO recordLogDTO = new AsyncTaskUpdateAndRecordLogDTO();
            recordLogDTO.setBizTaskId(bizTaskDO.getId());
            recordLogDTO.setCustomerId(bizTaskDO.getCustomerId());
            recordLogDTO.setReason(reason);
            recordLogDTO.setStartTime(new Date());
            recordLogDTO.setEndTime(new Date());
            recordLogDTO.setTaskStatus(TaskStatusEnum.FAIL);
            recordLogDTO.setBizPartCode(bizTaskDO.getBizPartCode());
            recordLogDTO.setBizCode(bizTaskDO.getBizCode());
            this.recordLogAndUpdateStatus(recordLogDTO);
            // 重新触发调度

            asyncTaskScheduleDTO.setLineupAPIError(Boolean.TRUE);
            asyncTaskScheduleDTO.setBizTaskDO(bizTaskDO);
            asyncTaskScheduleDTO.setIsUnQueuedExc(Boolean.FALSE);
            this.schedule(asyncTaskScheduleDTO);
        }
    }

    @Override
    public Boolean isUnQueuedExc(String errorCode) {
        return taskConfig.getUnQueuedExcList().contains(errorCode);
    }

    @Override
    public Boolean isRetryExc(AsyncTaskCheckRetryExcDTO asyncTaskCheckRetryExcDTO) {
        String script =
                Optional.ofNullable(asyncTaskCheckRetryExcDTO).map(AsyncTaskCheckRetryExcDTO::getBizTaskConfDO).map(BizTaskConfDO::getRetryRuleContent).orElse(null);
        String errorCode =
                Optional.ofNullable(asyncTaskCheckRetryExcDTO).map(AsyncTaskCheckRetryExcDTO::getErrorCode).orElse(null);
        if (StrUtil.isEmpty(script)) {
            log.info("规则={}||errorCode={}为空", script, errorCode);
            return false;
        }
        Map<String, Object> variables = new HashMap<>();
        variables.put("errorCode", errorCode);
        asyncTaskCheckRetryExcDTO.setVariables(variables);
        asyncTaskCheckRetryExcDTO.setScript(script);
        Boolean result = mvel2ScriptService.execute(asyncTaskCheckRetryExcDTO);
        return BooleanUtil.isTrue(result);
    }

    /**
     * where 条件 id,customerId
     * 更新任务更新字段：status、end_flag、reason
     * log字段：task_id,start_time，end_time，status
     */
    @Override
    public void recordLogAndUpdateStatus(AsyncTaskUpdateAndRecordLogDTO recordLogDTO) {
        ScheduleLogUtils.log("状态更新记录日志", recordLogDTO.getBizTaskId(), recordLogDTO.getCustomerId(), "recordLogDTO={}"
                , JSONUtil.toJsonStr(recordLogDTO));

        Boolean updateTaskRes = bizTaskService.updateTaskStatus(recordLogDTO.buildBizTaskDO());
        if (!updateTaskRes) {
            throw new BusinessException(ErrorCodeEnum.UPDATE_TASK_ERROR.getCode(), ErrorCodeEnum.UPDATE_TASK_ERROR.getMessage());
        }
        Boolean addLogRes = bizTaskLogService.recordLog(recordLogDTO.buildBizTaskLogDO());
        if (!addLogRes) {
            throw new BusinessException(ErrorCodeEnum.ADD_TASK_LOG.getCode(), ErrorCodeEnum.ADD_TASK_LOG.getMessage());
        }
    }

    @Override
    public TaskRespDTO createTask(TaskReqDTO taskReqDTO) {
        return this.transactionService.execute(transactionStatus -> {
            // 查询配置
            BizTaskConfDO bizTaskConfDO =
                    bizTaskConfManagement.getBizTaskConfByPartAndBizCodeOrTaskCode(taskReqDTO.getBizPartCode(),
                            taskReqDTO.getBizCode(), taskReqDTO.getTaskCode());
            BizTaskDO bizTaskDO = new BizTaskDO();
            bizTaskDO.setBizPartCode(StrUtil.emptyToDefault(taskReqDTO.getBizPartCode(), bizTaskConfDO.getBizPartCode()));
            bizTaskDO.setBizCode(StrUtil.emptyToDefault(taskReqDTO.getBizCode(), bizTaskConfDO.getBizCode()));
            bizTaskDO.setAreaCode(taskReqDTO.getAreaCode());
            bizTaskDO.setCustomerId(taskReqDTO.getCustomerId());
            bizTaskDO.setTaxNo(taskReqDTO.getTaxNo());
            bizTaskDO.setAccountId(taskReqDTO.getAccountId());
            bizTaskDO.setAccountType(taskReqDTO.getAccountType());
            bizTaskDO.setBusinessType(taskReqDTO.getBizType());
            bizTaskDO.setInterfaceName(bizTaskConfDO.getInterfaceName());
            bizTaskDO.setMethodName(bizTaskConfDO.getMethodName());
            bizTaskDO.setParamType(bizTaskConfDO.getParamType());
            bizTaskDO.setParameters(taskReqDTO.getBizParams());
            bizTaskDO.setBizTag(StringUtils.defaultIfBlank(taskReqDTO.getBizMqTag(),
                    bizTaskConfDO.getBizPartCode()));
            bizTaskDO.setExecType(null == taskReqDTO.getExecType() ? bizTaskConfDO.getExecType() : taskReqDTO.getExecType());
            bizTaskDO.setStatus(TaskStatusEnum.NOT_EXECUTE.getCode());
            bizTaskDO.setEndFlag(EndFlagEnum.UNEND.getCode());
            bizTaskDO.setExecuteNum(0);
            bizTaskDO.setExecuteRuleContent(StringUtils.defaultIfBlank(taskReqDTO.getExecRule(),
                    bizTaskConfDO.getExecuteRuleContent()));
            bizTaskDO.setRemark(taskReqDTO.getRemark());
            bizTaskDO.setTaskCode(taskReqDTO.getTaskCode());
            queuedTaskLineupInfo(bizTaskDO);
            Boolean result = bizTaskService.createTask(bizTaskDO);
            ScheduleLogUtils.log("创建任务-任务落库", bizTaskDO.getId(), taskReqDTO.getCustomerId(),
                    "result={}, bizTaskDO={}", result, JSONUtil.toJsonStr(bizTaskDO));
            if (!result) {
                throw new BusinessException(ErrorCodeEnum.CREATE_TASK_ERROR.getCode(),
                        ErrorCodeEnum.CREATE_TASK_ERROR.getMessage());
            }
            AsyncTaskScheduleDTO asyncTaskScheduleDTO = new AsyncTaskScheduleDTO();
            asyncTaskScheduleDTO.setBizTaskDO(bizTaskDO);
            asyncTaskScheduleDTO.setIsUnQueuedExc(Boolean.FALSE);
            AsyncTaskMergeDTO asyncTaskMergeDTO = merge(bizTaskDO, bizTaskConfDO);
            log.info("[创建任务]任务合并，bizTaskDO={}， bizTaskConfDO={},asyncTaskMergeDTO={}", bizTaskDO, bizTaskConfDO,
                    asyncTaskMergeDTO);

            if (asyncTaskMergeDTO.getIsExecTask()) {
                log.info("[创建任务]任务调度，customerId={},schedule入参={}", taskReqDTO.getCustomerId(),
                        JSONUtil.toJsonStr(asyncTaskScheduleDTO));
                ScheduleLogUtils.log("创建任务", bizTaskDO.getId(), bizTaskDO.getCustomerId(),
                        "当前任务可被调度");
                schedule(asyncTaskScheduleDTO);
            } else {
                ScheduleLogUtils.log("创建任务", bizTaskDO.getId(), bizTaskDO.getCustomerId(),
                        "当前任务被合并不执行");
            }
            return TaskRespDTO.buildSuccess(asyncTaskMergeDTO.getExecbizTaskId());
        });
    }

    @Override
    public int addUnQueueExecutedNum(BizTaskDO bizTaskDO) {
        String extend = Optional.ofNullable(bizTaskDO).map(BizTaskDO::getExtend).orElse(null);
        AsyncTaskExtendDTO asyncTaskExtendDTO = null;
        if (StrUtil.isNotBlank(extend)) {
            asyncTaskExtendDTO = JSON.parseObject(extend, AsyncTaskExtendDTO.class);
        }
        if (null == asyncTaskExtendDTO) {
            asyncTaskExtendDTO = new AsyncTaskExtendDTO();
        }
        Integer originExcNum = null == asyncTaskExtendDTO.getUnQueueExecutedNum() ? 0 :
                asyncTaskExtendDTO.getUnQueueExecutedNum();
        asyncTaskExtendDTO.setUnQueueExecutedNum(originExcNum + 1);
        bizTaskDO.setExtend(JSON.toJSONString(asyncTaskExtendDTO));
        bizTaskService.updateTaskExtend(bizTaskDO);
        return asyncTaskExtendDTO.getUnQueueExecutedNum();
    }

    /**
     * 遇到未排队异常时判断
     *
     * @param currentUnQueueExecuteNum
     * @return
     */
    @Override
    public boolean isUnqueueExcAble(Integer currentUnQueueExecuteNum) {
        int unQueuedExcNumConfig = taskConfig.getUnQueuedExcNumConfig();
        return null == currentUnQueueExecuteNum || currentUnQueueExecuteNum <= unQueuedExcNumConfig;
    }


    private String getCacheKey(BizTaskConfDO bizTaskConfDO, BizTaskDO bizTaskDO) {
        // 取md5计算字段范围
        String mergeRuleContent = bizTaskConfDO.getMergeRuleContent();
        List<String> fields = StrUtil.split(mergeRuleContent, ",");
        // 计算md5
        String md5 = MD5Util.calculateMD5(bizTaskDO, fields);
        if (StrUtil.isEmpty(md5)) {
            return null;
        }
        return TaskConfig.buildMergeKey(md5);
    }

    private Long getMergeTimeRangeByAreaCode(String mergeTimeRangeConf, String areaCode) {
        if (StrUtil.isEmpty(mergeTimeRangeConf)) {
            return null;
        }
        Map<String, Long> map = JSON.parseObject(mergeTimeRangeConf, new TypeReference<Map<String, Long>>() {
        });
        Long mergeTimeRange = map.get(areaCode);
        if (null == mergeTimeRange) {
            mergeTimeRange = map.get(AreacodeEnum.CN.getAreaCode());
        }
        return mergeTimeRange;
    }

    private void queuedTaskLineupInfo(BizTaskDO bizTaskDO) {
        if (ExecTypeEnum.QUEUED_EXEC.getCode().equals(bizTaskDO.getExecType())) {
            AsyncTaskExtendDTO asyncTaskExtendDTO =
                    Optional.ofNullable(bizTaskDO.getExtend()).map(ext -> JSON.parseObject(ext, AsyncTaskExtendDTO.class)).orElse(new AsyncTaskExtendDTO());
            asyncTaskExtendDTO.setCtxBizPartCode(TraceTags.getTagValue("bizPartCode"));
            asyncTaskExtendDTO.setCtxBizCode(TraceTags.getTagValue("bizCode"));
            asyncTaskExtendDTO.setCtxTicket(TraceTags.getTagValue("ticket"));
            asyncTaskExtendDTO.setXCtxBizPartCode(TraceUtils.getX17bootTraceMap().get(TraceConstants.X_17BOOT_CTX_PREFIX + "bizPartCode"));
            asyncTaskExtendDTO.setXCtxBizCode(TraceUtils.getX17bootTraceMap().get(TraceConstants.X_17BOOT_CTX_PREFIX + "bizCode"));
            asyncTaskExtendDTO.setXCtxTicket(TraceUtils.getX17bootTraceMap().get(TraceConstants.X_17BOOT_CTX_PREFIX + "ticket"));
            bizTaskDO.setExtend(JSON.toJSONString(asyncTaskExtendDTO));
        }
    }
}
