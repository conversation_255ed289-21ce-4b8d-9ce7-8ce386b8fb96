package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskConfigExtendDTO;
import cn.com.servyou.asynctask.shared.BizResultNotifyManagement;
import cn.com.servyou.i7boot.spring.ApplicationContextHolder;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/12/11 18:58
 */
@Slf4j
@Service
public class BizResultNotifyManagementImpl implements BizResultNotifyManagement {


    @Override
    public ResultNotifyRespDTO resultNotify(ResultNotifyReqDTO resultNotifyReqDTO,
                                            AsyncTaskConfigExtendDTO asyncTaskConfigExtendDTO) {
        try {
            Object invokeService = ApplicationContextHolder.getBean(asyncTaskConfigExtendDTO.getNotifyInterface());
            Class<?> paramClass = Class.forName(asyncTaskConfigExtendDTO.getNotifyParamType());
            Method method = invokeService.getClass().getMethod(asyncTaskConfigExtendDTO.getNotifyMethod(), paramClass);
            return (ResultNotifyRespDTO) method.invoke(invokeService, resultNotifyReqDTO);
        } catch (Exception e) {
            log.error("invoke, asyncTaskConfigExtendDTO:{}", JSON.toJSONString(asyncTaskConfigExtendDTO), e);
            return ResultNotifyRespDTO.failed(resultNotifyReqDTO.getAsyncTaskId(), null, e.getMessage());
        }
    }
}
