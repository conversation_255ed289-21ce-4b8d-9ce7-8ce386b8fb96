package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.asynctask.core.service.BizTaskConfService;
import cn.com.servyou.asynctask.core.utils.AssertUtil;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.asynctask.shared.BizTaskConfManagement;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/15 21:00
 */
@Slf4j
@Service
public class BizTaskConfManagementImpl implements BizTaskConfManagement {

    @Autowired
    private BizTaskConfService bizTaskConfService;


    @Override
    public BizTaskConfDO getBizTaskConfByPartAndBizCodeOrTaskCode(String bizPartCode, String bizCode, String taskCode) {
        // 查询配置
        BizTaskConfDO bizTaskConfDO = new BizTaskConfDO();
        bizTaskConfDO.setBizPartCode(bizPartCode);
        bizTaskConfDO.setBizCode(bizCode);
        bizTaskConfDO.setTaskCode(taskCode);
        BizTaskConfDO bizTaskConf;
        if (StrUtil.isEmpty(taskCode)) {
            bizTaskConf = bizTaskConfService.getBizTaskConfByPartAndBizCode(bizTaskConfDO);
        } else {
            bizTaskConf = bizTaskConfService.getBizTaskConfByTaskCode(bizTaskConfDO);
        }
        // 校验配置
        AssertUtil.notNull(bizTaskConf, ErrorCodeEnum.TASK_CONF_EMPTY.getCode(), ErrorCodeEnum.TASK_CONF_EMPTY.getMessage());
        return bizTaskConf;
    }
}
