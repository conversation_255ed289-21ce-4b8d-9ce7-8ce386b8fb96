package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.common.dto.BaseParamDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyReqDTO;
import cn.com.servyou.asynctask.common.dto.ResultNotifyRespDTO;
import cn.com.servyou.asynctask.core.domain.*;
import cn.com.servyou.asynctask.core.enums.EndFlagEnum;
import cn.com.servyou.asynctask.core.enums.ExecTypeEnum;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.asynctask.core.service.BizTaskService;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.com.servyou.asynctask.core.utils.SpringBeanUtils;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.shared.AsyncTaskScheduleManagement;
import cn.com.servyou.asynctask.shared.BizResultNotifyManagement;
import cn.com.servyou.asynctask.shared.BizTaskConfManagement;
import cn.com.servyou.asynctask.shared.TaskTicketManagement;
import cn.com.servyou.i7boot.trace.TraceUtils;
import cn.com.servyou.xqy.framework.rpc.facade.ErrorContext;
import cn.com.servyou.xqy.framework.rpc.facade.Result;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

@Slf4j
@Service
public class Callback {

//    @Autowired
//    private AsyncTaskScheduleManagement asyncTaskScheduleManagement;
//    @Autowired
//    private BizTaskConfManagement bizTaskConfManagement;
//    @Autowired
//    private TaskTicketManagement taskTicketManagement;
//    @Autowired
//    private BizTaskService bizTaskService;

    public void onreturn(Result msg, BaseParamDTO arg) {
        TraceUtils.executeInNewContext(getClass().getSimpleName(), "execute", () -> {
            log.info("接收到异步消息返回 msg:{}, param:{}", JSON.toJSONString(msg), JSON.toJSONString(arg));
            ScheduleLogUtils.log("任务执行异步回调结果", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "业务执行异步结果RETRUN, " +
                    "msg:{}, " + "param:{}", JSON.toJSONString(msg), JSON.toJSONString(arg));

            String reason =
                    Optional.ofNullable(msg).map(Result::getErrorContext).map(ErrorContext::getErrorMessage).orElse(null);
            String errorCode =
                    Optional.ofNullable(msg).map(Result::getErrorContext).map(ErrorContext::getErrorCode).orElse(null);
            invokeCallBack(arg, msg, msg.isSuccess(), reason, errorCode);
            return null;
        });
    }

    public void onthrow(Throwable ex, BaseParamDTO arg) {
        TraceUtils.executeInNewContext(getClass().getSimpleName(), "execute", () -> {
            log.info("接收到异步消息失败返回 param:{}", JSON.toJSONString(arg), ex);
            ScheduleLogUtils.log("任务执行异步回调结果", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "业务执行异步结果Throw, " +
                    "msg:{}, " + "param:{}", StrUtil.sub(ex.getMessage(), 0, 40), JSON.toJSONString(arg));
            String reason = Optional.ofNullable(ex).map(Throwable::getMessage).orElse(null);
            String errorCode = "DUBBO_ERROR_001";
            invokeCallBack(arg, null, Boolean.FALSE, reason, errorCode);
            return null;
        });
    }

    private boolean isRetryExc(String errorCode, BaseParamDTO arg, BizTaskConfDO conf) {
        AsyncTaskCheckRetryExcDTO asyncTaskCheckRetryExcDTO = new AsyncTaskCheckRetryExcDTO();
        asyncTaskCheckRetryExcDTO.setErrorCode(errorCode);
        asyncTaskCheckRetryExcDTO.setBizTaskConfDO(conf);
        asyncTaskCheckRetryExcDTO.setBizTaskId(arg.getAsyncTaskId());
        asyncTaskCheckRetryExcDTO.setCustomerId(arg.getAsyncCustomerId());
        AsyncTaskScheduleManagement asyncTaskScheduleManagement =
                SpringBeanUtils.getBean(AsyncTaskScheduleManagement.class);
        return asyncTaskScheduleManagement.isRetryExc(asyncTaskCheckRetryExcDTO);
    }

    /**
     * 是否还有执行次数
     * @param bizTaskDO
     * @return
     */
    private boolean isHaveSurplusExc(BizTaskDO bizTaskDO, boolean isUnQueuedExc) {
        AsyncTaskScheduleManagement asyncTaskScheduleManagement =
                SpringBeanUtils.getBean(AsyncTaskScheduleManagement.class);
        boolean haveSurplusExc = !asyncTaskScheduleManagement.isExecLimit(bizTaskDO);
        // 非已排队场景时： 如果可执行次数耗尽，则判断当前是否是未排队异常，未排队异常是否有可执行次数，若有则认为还可以执行。
        if (!ExecTypeEnum.QUEUED_EXEC.getCode().equals(bizTaskDO.getExecType()) && !haveSurplusExc) {
            if (isUnQueuedExc) {
                haveSurplusExc = isUnqueueExcAble(bizTaskDO);
            }
        }
        return haveSurplusExc;
    }

    /**
     * 未排队异常
     * @param errorCode
     * @return
     */
    private boolean isUnQueuedExc(String errorCode){
        AsyncTaskScheduleManagement asyncTaskScheduleManagement =
                SpringBeanUtils.getBean(AsyncTaskScheduleManagement.class);
        return asyncTaskScheduleManagement.isUnQueuedExc(errorCode);
    }

    /**
     * 未排队可执行
     * @param bizTaskDO
     * @return
     */
    private boolean isUnqueueExcAble(BizTaskDO bizTaskDO) {
        String extend = Optional.ofNullable(bizTaskDO).map(BizTaskDO::getExtend).orElse(null);
        AsyncTaskExtendDTO asyncTaskExtendDTO = null;
        if (StrUtil.isNotBlank(extend)) {
            asyncTaskExtendDTO = JSON.parseObject(extend, AsyncTaskExtendDTO.class);
        }
        Integer currentUnQueueExecuteNum =
                Optional.ofNullable(asyncTaskExtendDTO).map(AsyncTaskExtendDTO::getUnQueueExecutedNum).orElse(1);
        AsyncTaskScheduleManagement asyncTaskScheduleManagement =
                SpringBeanUtils.getBean(AsyncTaskScheduleManagement.class);
        return asyncTaskScheduleManagement.isUnqueueExcAble(currentUnQueueExecuteNum);
    }

    private BizTaskConfDO getBizTaskConfByPartAndBizCode(BizTaskDO bizTaskDO){
        BizTaskConfManagement bizTaskConfManagement = SpringBeanUtils.getBean(BizTaskConfManagement.class);
        return bizTaskConfManagement.getBizTaskConfByPartAndBizCodeOrTaskCode(bizTaskDO.getBizPartCode(),
                bizTaskDO.getBizCode(), bizTaskDO.getTaskCode());
    }

    private BizTaskDO getTaskById(BaseParamDTO arg) {
        BizTaskService bizTaskService = SpringBeanUtils.getBean(BizTaskService.class);
        // 查任务信息
        return bizTaskService.getTaskById(new AsyncTaskBaseDTO(arg.getAsyncCustomerId(),
                arg.getAsyncTaskId()));
    }
    private void releaseTicket(BizTaskDO bizTaskDO){
        TaskTicketManagement taskTicketManagement = SpringBeanUtils.getBean(TaskTicketManagement.class);
        taskTicketManagement.releaseTicket(bizTaskDO.getLineupTicket());
    }
    private ResultNotifyRespDTO resultNotify(ResultNotifyReqDTO resultNotifyReqDTO, AsyncTaskConfigExtendDTO configExtendDTO){
        BizResultNotifyManagement bizResultNotifyManagement = SpringBeanUtils.getBean(BizResultNotifyManagement.class);
        return bizResultNotifyManagement.resultNotify(resultNotifyReqDTO,
                configExtendDTO);
    }

    private void invokeCallBack(BaseParamDTO arg, Result rpcResult, Boolean isSuccess, String reason,
                                String errorCode) {
        BizTaskDO bizTaskDO = getTaskById(arg);
        if (StrUtil.isNotEmpty(bizTaskDO.getLineupTicket())) {
            releaseTicket(bizTaskDO);
            ScheduleLogUtils.log("任务执行异步回调结果", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "释放ticket成功, " +
                    "ticket={}", bizTaskDO.getLineupTicket());
            log.info("[任务执行异步回调结果]释放ticket成功");
        }

        if (StrUtil.equals(bizTaskDO.getEndFlag(), EndFlagEnum.END.getCode())) {
            //任务已经结束
            log.info("[任务执行异步回调结果]任务已经结束，不处理返回消息");
            ScheduleLogUtils.log("任务执行异步回调结果", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "任务已经结束，不处理返回消息");
            return;
        }

        BizTaskConfDO conf = getBizTaskConfByPartAndBizCode(bizTaskDO);

        boolean isRetryExc = isRetryExc(errorCode, arg, conf);
        boolean isUnQueuedExc = isUnQueuedExc(errorCode);
        boolean isHaveSurplusExc = isHaveSurplusExc(bizTaskDO, isUnQueuedExc);
        boolean isRetryAble = isRetryExc && isHaveSurplusExc;
        Boolean needNotifyResult = true;
        if (isSuccess || !isRetryExc || !isHaveSurplusExc) {
            // 接口执行成功 或 不可重试异常 或 执行次数达到上限，需要结果通知
            AsyncTaskConfigExtendDTO configExtendDTO = Optional.ofNullable(conf).map(BizTaskConfDO::getExtend).map(AsyncTaskConfigExtendDTO::toDTO).orElse(null);
            // 是否满足通知条件
            if (configExtendDTO != null && configExtendDTO.isNotifyAble()) {

                ResultNotifyReqDTO resultNotifyReqDTO = new ResultNotifyReqDTO();
                resultNotifyReqDTO.setAsyncTaskId(arg.getAsyncTaskId());
                resultNotifyReqDTO.setBizPartCode(bizTaskDO.getBizPartCode());
                resultNotifyReqDTO.setBizCode(bizTaskDO.getBizCode());
                resultNotifyReqDTO.setBizParams(bizTaskDO.getParameters());
                resultNotifyReqDTO.setSuccess(isSuccess);
                resultNotifyReqDTO.setErrorCode(errorCode);
                resultNotifyReqDTO.setMessage(reason);
                resultNotifyReqDTO.setTaskCode(bizTaskDO.getTaskCode());
                // 未达可执行次数且当前异常可重试
                resultNotifyReqDTO.setRetryAble(isRetryAble);
                if (!isSuccess || rpcResult instanceof SingleResult) {
                    resultNotifyReqDTO.setData(((SingleResult<?>) rpcResult).getEntity());
                    log.info("resultNotifyReqDTO={}", JSON.toJSONString(resultNotifyReqDTO));

                    ResultNotifyRespDTO resultNotifyRespDTO = resultNotify(resultNotifyReqDTO, configExtendDTO);
                    needNotifyResult = false;
                    ScheduleLogUtils.log("任务执行结果通知-发送", arg.getAsyncTaskId(), arg.getAsyncCustomerId(),
                            "resultNotifyReqDTO.success={}, resultNotifyRespDTO={}", isSuccess, JSON.toJSONString(resultNotifyRespDTO));
                    if (!resultNotifyRespDTO.isSuccess()) {
                        isSuccess = resultNotifyRespDTO.isSuccess();
                        reason = resultNotifyRespDTO.getMessage();
                        errorCode = resultNotifyRespDTO.getErrorCode();
                        isRetryExc = isRetryExc(errorCode, arg, conf);
                    }

                } else {
                    log.error("暂不支持的接口返回类型");
                    ScheduleLogUtils.log("任务执行结果通知", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "结果isSuccess[{}]，isRetryExc[{}]满足通知, 配置满足通知, 但接口返回类型不满足通知类型:{}", isSuccess, isRetryExc, rpcResult.getClass().getSimpleName());
                }
            } else {
                ScheduleLogUtils.log("任务执行结果通知", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "结果isSuccess[{}]，isRetryExc[{}]满足通知, 但配置不满足通知, configExtendDTO={}", isSuccess, isRetryExc, JSON.toJSONString(configExtendDTO));
            }
        } else {
            ScheduleLogUtils.log("任务执行结果通知", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "结果isSuccess[{}]，isRetryExc[{}]不满足通知", isSuccess, isRetryExc);
        }

        resultHandle(bizTaskDO, arg, isSuccess, reason, errorCode, isRetryExc, isUnQueuedExc, needNotifyResult);
    }

    public void resultHandle(BizTaskDO bizTaskDO, BaseParamDTO arg, Boolean isSuccess,
                             String reason, String errorCode, boolean isRetryExc, boolean isUnQueuedExc,
                             boolean needNotifyResult) {

        AsyncTaskScheduleManagement asyncTaskScheduleManagement =
                SpringBeanUtils.getBean(AsyncTaskScheduleManagement.class);
        AsyncTaskUpdateAndRecordLogDTO recordLogDTO = new AsyncTaskUpdateAndRecordLogDTO();
        recordLogDTO.setBizPartCode(bizTaskDO.getBizPartCode());
        recordLogDTO.setBizCode(bizTaskDO.getBizCode());
        recordLogDTO.setBizTaskId(bizTaskDO.getId());
        recordLogDTO.setCustomerId(bizTaskDO.getCustomerId());

        recordLogDTO.setReason(String.format("[traceId:%s][errorCode:%s] %s",
                StringUtils.defaultIfBlank(TraceUtils.getTraceId(), ""), StringUtils.defaultIfBlank(errorCode, ""),
                StringUtils.defaultIfBlank(reason, "")));

        recordLogDTO.setStartTime(new Date(arg.getAsyncStartTime()));
        recordLogDTO.setEndTime(new Date());

        if (isSuccess) {
            recordLogDTO.setTaskStatus(TaskStatusEnum.SUCCESS);
            recordLogDTO.setEndFlag(EndFlagEnum.END);
        } else {
            recordLogDTO.setTaskStatus(TaskStatusEnum.FAIL);


            if (isRetryExc) {
                ScheduleLogUtils.log("任务执行异步回调结果", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "异常可重试，重新任务调度");

                AsyncTaskScheduleDTO asyncTaskScheduleDTO = new AsyncTaskScheduleDTO();
                asyncTaskScheduleDTO.setBizTaskDO(bizTaskDO);
                //是否排队异常
                asyncTaskScheduleDTO.setIsUnQueuedExc(isUnQueuedExc);
                asyncTaskScheduleDTO.setNeedNotifyResult(needNotifyResult);
                asyncTaskScheduleManagement.schedule(asyncTaskScheduleDTO);
            } else {
                ScheduleLogUtils.log("任务执行异步回调结果", arg.getAsyncTaskId(), arg.getAsyncCustomerId(), "异常不可重试，任务结束");
                recordLogDTO.setEndFlag(EndFlagEnum.END);
            }
        }
        asyncTaskScheduleManagement.recordLogAndUpdateStatus(recordLogDTO);
    }


}

