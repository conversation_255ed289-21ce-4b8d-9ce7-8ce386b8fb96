package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.core.common.MvelScriptServiceImpl;
import cn.com.servyou.asynctask.core.domain.AsyncTaskExtendDTO;
import cn.com.servyou.asynctask.core.enums.ExecTypeEnum;
import cn.com.servyou.asynctask.core.service.RedisService;
import cn.com.servyou.asynctask.core.utils.ScheduleLogUtils;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.sdk.TaskLifecycle;
import cn.com.servyou.asynctask.sdk.dto.TaskContext;
import cn.com.servyou.asynctask.shared.TaskInvokeManagement;
import cn.com.servyou.i7boot.spring.ApplicationContextHolder;
import cn.com.servyou.i7boot.trace.TraceConstants;
import cn.com.servyou.i7boot.trace.TraceUtils;
import cn.com.servyou.xqy.framework.core.TraceTags;
import cn.com.servyou.xqy.framework.exception.BusinessException;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class TaskInvokeManagementImpl implements TaskInvokeManagement {

    @Autowired
    MvelScriptServiceImpl scriptService;
    @Autowired
    RedisService redisService;

    @Override
    public void doBusinessRequest(BizTaskDO bizTaskDO) {
        log.info("[业务执行]-{}-invoke start", bizTaskDO.getId());
        ScheduleLogUtils.log("业务执行", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "invoke start");

        setCtxLineupInfo(bizTaskDO);

        String interfaceName = bizTaskDO.getInterfaceName();
        Object invokeService = ApplicationContextHolder.getBean(interfaceName);
        //生命周期bean 调用逻辑
        if (invokeService instanceof TaskLifecycle) {
            TaskLifecycle taskLifeBean = (TaskLifecycle) invokeService;
            TaskContext taskContext = BeanUtil.copyProperties(bizTaskDO, TaskContext.class);

            //前置逻辑
            taskContext = taskLifeBean.preExecute(taskContext);
            Long bizTaskId = taskContext.getBizTaskId();
            String uniqueKeyValue = taskContext.getUniqueKeyValue();
            if (StrUtil.isNotEmpty(uniqueKeyValue)) {
                //如果redis 里 存在站位信息,不重复执行网关调用
                String runningTaskId = redisService.get(uniqueKeyValue);
                if (StrUtil.isNotEmpty(runningTaskId)) {
                    //redisService.set(runningTaskId, bizTaskId.toString(), 60 * 60 * 24, 0);
                    log.info("[业务执行]-{}-invoke end", bizTaskDO.getId());
                    ScheduleLogUtils.log("业务执行", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "invoke end");
                    return;
                }

            }
            //redisService.set(uniqueKeyValue, bizTaskId.toString(), 60 * 60 * 24, 0);
            //redisService.set(uniqueKeyValue, bizTaskId.toString(), 60 * 60 * 24, 0);
            //调用逻辑
            Object invokeParams = taskContext.getInvokeParams();
            Map<String, Object> context = MapUtil.of("request", invokeParams);
            scriptService.exec("gatewayClient.exec(request)", context);


        } else {

            String methodName = bizTaskDO.getMethodName();
            String paramType = bizTaskDO.getParamType();
            String args = bizTaskDO.getParameters();

            try {
                JSONObject jsonObject = JSON.parseObject(args);
                jsonObject.put("asyncTaskId", bizTaskDO.getId());
                jsonObject.put("asyncCustomerId", bizTaskDO.getCustomerId());
                jsonObject.put("asyncStartTime", System.currentTimeMillis());
                doInvoke(invokeService, methodName, paramType, jsonObject.toJSONString());


                log.info("[业务执行]-{}-invoke end", bizTaskDO.getId());
                ScheduleLogUtils.log("业务执行", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "invoke end");
            } catch (Exception e) {
                ScheduleLogUtils.log("业务执行", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "接口调用异常");
                log.error("调用接口请求有误，请检查接口及参数配置", e);
                throw new BusinessException("CONF_ERROR", "接口调用异常");
            } finally {
                TraceUtils.remove(String.format("%s%s", TraceConstants.X_17BOOT_CTX_PREFIX, "bizPartCode"));
                TraceUtils.remove(String.format("%s%s", TraceConstants.X_17BOOT_CTX_PREFIX, "bizCode"));
                TraceUtils.remove(String.format("%s%s", TraceConstants.X_17BOOT_CTX_PREFIX, "ticket"));
                TraceTags.removeTag("bizPartCode");
                TraceTags.removeTag("bizCode");
                TraceTags.removeTag("ticket");
            }
        }

    }

    //根据接口方法名调用接口
    private Object doInvoke(Object invokeService, String methodName, String paramType, String args) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, NoSuchFieldException, ClassNotFoundException {

        Class<?> paramClass = Class.forName(paramType);
        Method method = invokeService.getClass().getMethod(methodName, paramClass);

        return method.invoke(invokeService, JSON.parseObject(args, paramClass));

    }


    /**
     * 处理上下文排队信息。已排场景从extend取，创建任务时从上下文中取出存下。
     *
     * @param bizTaskDO
     */
    private void setCtxLineupInfo(BizTaskDO bizTaskDO) {
        String invokeBizPartCode;
        String invokeBizCode;
        String invokeTicket;
        if (ExecTypeEnum.QUEUED_EXEC.getCode().equals(bizTaskDO.getExecType())) {
            AsyncTaskExtendDTO asyncTaskExtendDTO = Optional.ofNullable(bizTaskDO.getExtend()).map(ext -> JSON.parseObject(ext, AsyncTaskExtendDTO.class)).orElse(new AsyncTaskExtendDTO());
            invokeBizPartCode = StrUtil.isNotBlank(asyncTaskExtendDTO.getCtxBizPartCode()) ? asyncTaskExtendDTO.getCtxBizPartCode() : asyncTaskExtendDTO.getXCtxBizPartCode();
            invokeBizCode = StrUtil.isNotBlank(asyncTaskExtendDTO.getCtxBizCode()) ? asyncTaskExtendDTO.getCtxBizCode() : asyncTaskExtendDTO.getXCtxBizCode();
            invokeTicket = StrUtil.isNotBlank(asyncTaskExtendDTO.getCtxTicket()) ? asyncTaskExtendDTO.getCtxTicket() : asyncTaskExtendDTO.getXCtxTicket();
        } else {
            invokeBizPartCode = bizTaskDO.getBizPartCode();
            invokeBizCode = bizTaskDO.getBizCode();
            invokeTicket = bizTaskDO.getLineupTicket();
        }

        TraceTags.putTagValue("bizPartCode", invokeBizPartCode);
        TraceTags.putTagValue("bizCode", invokeBizCode);
        TraceTags.putTagValue("ticket", invokeTicket);
        TraceUtils.setTraceValue(String.format("%s%s", TraceConstants.X_17BOOT_CTX_PREFIX, "bizPartCode"), invokeBizPartCode);
        TraceUtils.setTraceValue(String.format("%s%s", TraceConstants.X_17BOOT_CTX_PREFIX, "bizCode"), invokeBizCode);
        TraceUtils.setTraceValue(String.format("%s%s", TraceConstants.X_17BOOT_CTX_PREFIX, "ticket"), invokeTicket);
        ScheduleLogUtils.log("业务执行", bizTaskDO.getId(), bizTaskDO.getCustomerId(), "bizPartCode={},bizCode={},ticket={}",
                StrUtil.isBlank(TraceTags.getTagValue("bizPartCode")) ? TraceUtils.getX17bootTraceMap().get(TraceConstants.X_17BOOT_CTX_PREFIX + "bizPartCode") : TraceTags.getTagValue("bizPartCode"),
                StrUtil.isBlank(TraceTags.getTagValue("bizCode")) ? TraceUtils.getX17bootTraceMap().get(TraceConstants.X_17BOOT_CTX_PREFIX + "bizCode") : TraceTags.getTagValue("bizCode"),
                StrUtil.isBlank(TraceTags.getTagValue("ticket")) ? TraceUtils.getX17bootTraceMap().get(TraceConstants.X_17BOOT_CTX_PREFIX + "ticket") : TraceTags.getTagValue("ticket"));
    }


}
