package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.core.enums.ErrorCodeEnum;
import cn.com.servyou.asynctask.core.utils.AssertUtil;
import cn.com.servyou.asynctask.dataobj.BizTaskDO;
import cn.com.servyou.asynctask.integration.TaskTicketClient;
import cn.com.servyou.asynctask.shared.TaskTicketManagement;
import cn.com.servyou.asynctask.shared.convert.TaskTicketConvert;
import cn.com.servyou.taxlineup.facade.dto.InLineReqDTO;
import cn.com.servyou.taxlineup.facade.dto.InLineRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: TaskTicketManagementimpl
 * @since 2024/8/19
 */
@Slf4j
@Service
public class TaskTicketManagementImpl implements TaskTicketManagement {

    @Autowired
    private TaskTicketClient taskTicketClient;

    @Override
    public InLineRespDTO getTicket(BizTaskDO bizTaskDO) {
        checkInLineReqDTO(bizTaskDO);
        InLineReqDTO inlineReqDTO = TaskTicketConvert.toInLineReqDTO(bizTaskDO);
        return taskTicketClient.getTicket(inlineReqDTO);
    }

    @Override
    public void releaseTicket(String ticket) {
        AssertUtil.notEmpty(ErrorCodeEnum.TICKET_NOT_EMPTY,ticket);
        taskTicketClient.releaseTicket(ticket);
    }

    private void checkInLineReqDTO(BizTaskDO bizTaskDO) {
        AssertUtil.notNull(bizTaskDO, ErrorCodeEnum.COMMON_PARAM_NOT_EMPTY);
        AssertUtil.notNull(bizTaskDO.getCustomerId(), ErrorCodeEnum.COMPANY_ID_NOT_EMPTY);
        AssertUtil.notEmpty(ErrorCodeEnum.TAX_NO_NOT_EMPTY,bizTaskDO.getTaxNo());
        AssertUtil.notEmpty(ErrorCodeEnum.AREA_CODE_NOT_EMPTY,bizTaskDO.getAreaCode());
        AssertUtil.notEmpty(ErrorCodeEnum.BIZ_PART_CODE_NOT_EMPTY,bizTaskDO.getBizPartCode());
        AssertUtil.notEmpty(ErrorCodeEnum.BIZ_CODE_NOT_EMPTY,bizTaskDO.getBizCode());
    }
}
