package cn.com.servyou.asynctask.shared.impl;

import cn.com.servyou.asynctask.shared.TestManagement;
import cn.com.servyou.asynctask.shared.producer.RocketMQTestProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Slf4j
@Service
public class TestManagementImpl implements TestManagement {

    @Autowired
    private RocketMQTestProducer rocketMQTestProducer;

    @Override
    public void testMq(String msg) {
        log.info("入参：{}", msg);
        rocketMQTestProducer.send(msg.getBytes(StandardCharsets.UTF_8));
    }
}
