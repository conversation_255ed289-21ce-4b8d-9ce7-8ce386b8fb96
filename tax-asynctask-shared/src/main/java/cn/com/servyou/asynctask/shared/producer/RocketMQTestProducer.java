package cn.com.servyou.asynctask.shared.producer;

import com.servyou.elephant.mq.Body;
import com.servyou.elephant.mq.Delay;
import com.servyou.elephant.mq.Key;
import com.servyou.elephant.mq.MessageResult;
import com.servyou.elephant.mq.Parameter;
import com.servyou.elephant.mq.ProducerConfig;
import com.servyou.elephant.mq.Tag;
import com.servyou.elephant.mq.Transaction;
import com.servyou.elephant.mq.UserProperties;

import java.util.Map;

/**
 * Elephant RocketMQ 使用示例
 * http://docs.dc.servyou-it.com/pages/viewpage.action?pageId=24838654
 */
public interface RocketMQTestProducer {

    /**
     * 普通消息发送。结合了spring的事务机制，在事务提交之后，才会发送消息。
     * 消息体可以为任意类型。
     * 如果定义的是实体，默认使用json序列化之后送。
     * 也可以是byte[]
     */
    @ProducerConfig(topic = "${rocketmq.topic}")
    void send(@Body String content);

    /**
     * 将会以注解上指定的topic, tag, key, userProperties发送，tag，key,
     * userProperties在发送时不是必须的。@Body注
     * 解的参数部分将序列化成json并进行发送。具体形式见上方表格。
     */
    @ProducerConfig(topic = "${rocketmq.topic}", tag = "${rocketmq.tag}")
    void send(@Body String myMessage, @Key String key, @UserProperties Map<String, String> userProperties);

    /**
     * 将会以注解上指定的topic, tag发送，同时@Body注
     * 解的参数为字节数组，具体发送形式见上方表格。
     * @Body注解的参数可以为任意类型，除了字符串，字节数组，其余均为转换成json
     */
    @ProducerConfig(topic = "${rocketmq.topic}", tag = "${rocketmq.tag}")
    void send(@Body byte[] message);

    /**
     * 将会以注解上指定的topic发送，这里的示例有一个@Tag注解，效果同@ProducerConfig中的tag，
     * 方法参数中存在多个@Parameter注解，会组成一个json进行发送，内容格式：{"p1":p1, "p2":p2}
     */
    @ProducerConfig(topic = "${rocketmq.topic}")
    void send(@Parameter("p1") String p1, @Parameter("p2") String p2, @Tag String tag);

    /**
     * 延时消息。RocketMQ支持1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h这几个时间点。
     * 发送时可以指定时间。Delay注解支持Date/Calendar/Instant/LocalDateTime，在该指定时间发送。
     * 也支持Long/Integer，默认单位为秒，当前时间 + delay之后发送。
     *
     * 如果发送时要求时间是3h后，则会设置为2h，因为RocketMQ不支持3h，以此类推。
     *
     * @param content
     * @param delay
     */
    @ProducerConfig(topic = "${rocketmq.topic}")
    void sendWithDelay(@Body String content, @Delay long delay);

    /**
     * 这里transaction = AFTER，采用spring的事务机制，在事务提交之后，才会发送消息。可能存在数据库事务提交成功，发送失败的可能性。
     * 使用jdk的序列化方式来序列化消息。建议不要设置，默认json
     */
    @ProducerConfig(topic = "${rocketmq.topic}", transaction = Transaction.AFTER)
    MessageResult sendAfterTransaction(@Body String content);
}
