package cn.com.servyou.asynctask.shared.producer;

import com.servyou.elephant.mq.Body;
import com.servyou.elephant.mq.Delay;
import com.servyou.elephant.mq.Key;
import com.servyou.elephant.mq.ProducerConfig;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/16 17:10
 */
public interface TaskScheduleProducer {

    @ProducerConfig(topic = "${rocketmq.producer.asynctask.schedule.topic}")
    void send(@Body String msg, @Key String key, @Delay int delayTime);

    /**
     * 实时调度，及时任务使用
     *
     * @param msg
     * @param key
     */
    @ProducerConfig(topic = "${rocketmq.producer.asynctask.intimeschedule.topic:tax-asynctask-topic-intimeschdule-notify}")
    void send(@Body String msg, @Key String key);

}
