<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- maven模型版本 maven2/maven3 使用4.0.0-->
    <modelVersion>4.0.0</modelVersion>
    <!-- 父级项目坐标 (默认从本地查找,未找到再到远程构件仓库查找)-->
    <parent>
        <groupId>cn.com.servyou</groupId>
        <artifactId>tax-asynctask</artifactId>
        <version>${version.number}-${build.number}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- 项目坐标 -->
    <artifactId>tax-asynctask-test</artifactId>

    <dependencies>
        <!-- framework dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.7.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <version>1.9.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>17boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.servyou</groupId>
            <artifactId>tax-asynctask-facadeimpl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.0.0</version>
            <scope>compile</scope>
        </dependency>


        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.7</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.7</version>
            <scope>compile</scope>
        </dependency>



        <!-- other dependencies -->
    </dependencies>

</project>