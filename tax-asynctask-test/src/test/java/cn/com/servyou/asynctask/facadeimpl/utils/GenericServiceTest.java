package cn.com.servyou.asynctask.facadeimpl.utils;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.MethodConfig;
import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import com.alibaba.dubbo.rpc.service.GenericService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: GenericServiceTest.java v 0.1 2025/2/27 20:57 pez1420 Exp $$
 */
public class GenericServiceTest {

    /**
     * 1.泛化调用主要用于跨业务的调用:
     *  对于跨业务调用场景，两个业务之间的接口交互，不应该依赖对方的代码，也就是不应该要求对方提供一个jar包。
     * 2.泛化调用虽然具有诸多优点，但也存在一定的性能开销。因此，在实际使用中应权衡利弊，避免过度使用泛化调用导致系统性能下降。
     * 3.发起泛化调用：通过Dubbo的API或注解等方式发起泛化调用，调用时只需传入服务接口及相应的参数即可。
     */

    public static void main(String[] args) {
        invokeCreateTaskTest();
    }
    public static void invokeCreateTaskTest() {
        ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
        reference.setInterface("cn.com.servyou.asynctask.facade.TaskFacade"); // 服务接口名
        reference.setVersion("1.0.0");
        reference.setGroup("dntax-test");

        ApplicationConfig applicationConfig = new ApplicationConfig();
        applicationConfig.setName("tax-asynctask");
        reference.setApplication(applicationConfig);

        RegistryConfig registryConfig = new RegistryConfig();
        registryConfig.setProtocol("zookeeper");
        registryConfig.setAddress("**************:2181,**************:2181,**************:2181");
        reference.setRegistry(registryConfig);

        reference.setGeneric(true); // 开启泛化调用
        reference.setAsync(false);
        reference.setTimeout(5000);
        reference.setCheck(false);
        reference.setId("taskFacadeImpl");

//        List<MethodConfig> methods = Lists.newArrayList();
//        MethodConfig methodConfig = new MethodConfig();
//        methodConfig.setName("createTask");
//        methodConfig.setAsync(true);
//        methodConfig.setOnreturnMethod("notify.onreturn");
//        methodConfig.setOnthrowMethod("notify.onthrow");
//        methods.add(methodConfig);
//        reference.setMethods(methods);

        GenericService genericService = reference.get();

        /**
         * {
         *     "accountId": "20200902000017290024010000082",
         *     "areaCode": "510000",
         *     "bizCode": "autoSyncStatus",
         *     "bizParams": "{\"accountId\":\"20200902000017290024010000082\",\"customerId\":\"*************\",\"period\":\"202501\"}",
         *     "bizPartCode": "SaasDeclare",
         *     "customerId": "*************",
         *     "taxNo": "915111813457775521"
         * }
         */
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("accountId", "20200902000017290024010000082");
        map.put("areaCode", "510000");
        map.put("bizCode", "autoSyncStatus");
        map.put("bizParams",
                "{\"accountId\":\"20200902000017290024010000082\",\"customerId\":\"*************\",\"period\":\"202501\"}");
        map.put("bizPartCode", "SaasDeclare");
        map.put("customerId", "*************");
        map.put("taxNo", "915111813457775521");

        Object result = genericService.$invoke("createTask",
                new String[] { "cn.com.servyou.asynctask.facade.dto.TaskReqDTO" },
                new Object[] { map });
        System.out.println(result.toString());
    }


}
