package cn.com.servyou.asynctask.facadeimpl.utils;

import org.junit.jupiter.api.Test;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.io.*;
import java.lang.reflect.Method;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version $Id: RedisClusterSplitTest.java v 0.1 2025/2/24 16:14 pez1420 Exp $$
 */
public class RedisClusterSplitTest {

    private static String getFilePath(String fileName) {
        URL resource = RedisClusterSplitTest.class.getClassLoader().getResource(fileName);
        String path = resource.getPath();
        return path;
    }

    @Test
    public void txtToCsvTest() {
        String outputFile = "_output_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
                            + ".csv";

        List<String> serviceNameList = new ArrayList<>();
        serviceNameList.add("xtax");
        serviceNameList.add("taxmanage");
        serviceNameList.add("taxcore");
        serviceNameList.add("taxbase");

        for (int i = 0; i < serviceNameList.size(); i++) {
            String serviceName = serviceNameList.get(i);
            String inputFile = getFilePath(serviceName + ".txt");
            try (BufferedReader br = new BufferedReader(new FileReader(inputFile));
                    BufferedWriter bw = new BufferedWriter(new FileWriter(outputFile, true))) {
                // 写入CSV表头
                if (i == 0) {
                    bw.write("项目,文件名,行号,具体代码");
                    bw.newLine();
                }

                String line;
                while ((line = br.readLine()) != null) {
                    String[] parts = line.split(":", 3);
                    if (parts.length < 3)
                        continue;

                    String csvLine = String.join(",", serviceName, parts[0], parts[1],
                        "\"" + parts[2].replace("\"", "\"\"") + "\"");
                    bw.write(csvLine);
                    bw.newLine();
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        System.out.println("转换完成，生成文件: " + outputFile);

    }

    @Test
    public void extractMethodName() {
        Class<?> targetInterface = D.class; // 替换为实际接口
        String result = parseInterfaceMethods(targetInterface);
        System.out.println("Unique methods: " + result);

    }

    /**
     * 解析接口及其父接口的所有方法（三步合一）
     * @param interfaceClass 目标接口的Class对象
     * @return 去重后合并的方法名字符串
     */
    public static String parseInterfaceMethods(Class<?> interfaceClass) {
        // 验证输入是否为接口
        if (!interfaceClass.isInterface()) {
            throw new IllegalArgumentException("Input must be an interface");
        }

        // 步骤1+2：递归获取所有方法并去重
        Set<String> methods = new TreeSet<>();
        collectMethodsRecursively(interfaceClass, methods);

        // 步骤3：合并为字符串
        return String.join("|", methods);
    }

    /**
     * 递归收集接口及其父接口的方法
     */
    private static void collectMethodsRecursively(Class<?> clazz, Set<String> collector) {
        // 获取当前接口声明的方法
        Arrays.stream(clazz.getMethods()).map(Method::getName).forEach(collector::add);

        // 递归处理父接口
        for (Class<?> parent : clazz.getInterfaces()) {
            collectMethodsRecursively(parent, collector);
        }
    }

}

interface A {
    Object get(String var1, String var2);

    int size(String var1);

    Set<String> keySet(String var1);

    boolean isExist(String var1, String var2);

    Map<Object, Object> getMapAll(String var1, String var2);

    List<Object> getMultiFromMap(String var1, String var2, List var3);

    Object getFromMap(String var1, String var2, Object var3);

    Set<String> getKeys(String var1, String var2);
}

interface B {
    boolean putString(String var1, String var2, String var3);

    String getString(String var1, String var2);

    Long hset(String var1, String var2, String var3, String var4);

    Set<String> hkeys(String var1, String var2);

    Long hlen(String var1, String var2);

    Boolean hexists(String var1, String var2, String var3);

    Long sadd(String var1, String var2, String... var3);

    Set<String> smembers(String var1, String var2);

    String srandmember(String var1, String var2);

    List<String> srandmember(String var1, String var2, int var3);

    Long srem(String var1, String var2, String... var3);

    String spop(String var1, String var2);

    Boolean sismember(String var1, String var2, String var3);

    Long zadd(String var1, String var2, double var3, String var5);

    Long zrem(String var1, String var2, String... var3);

    Long zcount(String var1, String var2, double var3, double var5);

    Long zcount(String var1, String var2, String var3, String var4);

    Long zlexcount(String var1, String var2, String var3, String var4);

    Set<String> zrange(String var1, String var2, long var3, long var5);

    Set<String> zrevrange(String var1, String var2, long var3, long var5);

    Set<String> zrangeByScore(String var1, String var2, double var3, double var5);

    Set<String> zrevrangeByScore(String var1, String var2, double var3, double var5);

    Long incr(String var1, String var2);

    Long incrBy(String var1, String var2, long var3);

    Long ttl(String var1, String var2);

    Long nativeExpire(String var1, String var2, int var3);

    Long setnx(String var1, String var2, String var3);

    Long hsetnx(String var1, String var2, String var3, String var4);

    String setex(String var1, String var2, int var3, String var4);

    String psetex(String var1, String var2, long var3, String var5);

    Long hdel(String var1, String var2, String... var3);

    Long zcard(String var1, String var2);

    Double zscore(String var1, String var2, String var3);

    Long zremrangeByScore(String var1, String var2, double var3, double var5);

    Long zremrangeByScore(String var1, String var2, String var3, String var4);

    String hmset(String var1, String var2, Map<String, String> var3);

    List<String> hmget(String var1, String var2, String... var3);

    Map<String, String> hgetAll(String var1, String var2);

    ScanResult<String> scan(String var1, String var2, ScanParams var3);

    String hmset(String var1, byte[] var2, Map<byte[], byte[]> var3);

    List<byte[]> hmget(String var1, byte[] var2, byte[]... var3);

    Map<byte[], byte[]> hgetAll(String var1, byte[] var2);

    Long del(String var1, byte[] var2);

    byte[] get(String var1, byte[] var2);

    Long ttl(String var1, byte[] var2);

    String set(String var1, byte[] var2, byte[] var3, byte[] var4, byte[] var5, long var6);
}

interface C {
    boolean clear(String var1);

    boolean put(String var1, String var2, Object var3);

    boolean put(String var1, String var2, Object var3, Date var4);

    boolean put(String var1, String var2, Object var3, int var4);

    Object get(String var1, String var2);

    boolean remove(String var1, String var2);

    int size(String var1);

    Set<String> keySet(String var1);

    boolean isExist(String var1, String var2);
}

interface D extends C, B, A {
    Map<Object, Object> getMapAll(String var1, String var2);

    List<Object> getMultiFromMap(String var1, String var2, List var3);

    Object getFromMap(String var1, String var2, Object var3);

    void putToMap(String var1, String var2, Object var3, Object var4);

    void putToMap(String var1, String var2, Map<Object, Object> var3);

    void removeFromMap(String var1, String var2, Object var3);

    Long expire(String var1, String var2, int var3);

    Object getSetWithLock(String var1, String var2, int var3);

    Set<String> getKeys(String var1, String var2);

    boolean remove(String var1, String[] var2);

    long setNx(String var1, String var2, String var3);
}