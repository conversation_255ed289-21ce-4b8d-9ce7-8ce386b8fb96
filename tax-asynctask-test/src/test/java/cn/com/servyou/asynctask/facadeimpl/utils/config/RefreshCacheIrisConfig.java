package cn.com.servyou.asynctask.facadeimpl.utils.config;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;

import lombok.extern.slf4j.Slf4j;

/**
 * 可刷新的缓存配置：
 * 用于处理从供应商获取原始数据（可能是JSON），然后通过解析函数转换成Java对象。
 * 用户提到每次使用都要转换，影响性能，所以应该需要缓存机制，避免重复解析。
 *
 * <AUTHOR>
 * @version $Id: RefreshIrisConfig.java v 0.1 2025/3/5 15:43 pez1420 Exp $$
 */
@Slf4j
public class RefreshCacheIrisConfig<I, R> {

    /** 上一次原始数据*/
    private AtomicReference<I>   lastOriginalData = new AtomicReference<>();

    /** 上一次解析结果 */
    private AtomicReference<R>   lastCacheResult  = new AtomicReference<>();

    /** 最新原始数据*/
    private AtomicReference<I>   originalData     = new AtomicReference<>();

    /** 最新解析结果 */
    private AtomicReference<R>   cacheResult      = new AtomicReference<>();

    /** 配置名称 */
    private final String         name;

    /** 提供原始json串数据*/
    private final Supplier<I>    supplierFunction;

    /** josn 通过解析函数转换成Java对象 */
    private final Function<I, R> parserFunction;

    public RefreshCacheIrisConfig(String name, Supplier<I> supplierFunction,
                                  Function<I, R> parserFunction) {
        this.name = name;
        this.supplierFunction = supplierFunction;
        this.parserFunction = parserFunction;
    }

    public R get() {
        R result = null;
        I rawData = supplierFunction.get();
        // 原始数据发生变化
        if (!Objects.equals(rawData, this.originalData.get())) {
            //DCL
            synchronized (this) {
                rawData = supplierFunction.get();
                if (!Objects.equals(rawData, this.originalData.get())) {
                    long startTime = System.currentTimeMillis();
                    try {
                        result = parserFunction.apply(rawData);

                        lastOriginalData.set(originalData.get());
                        lastCacheResult.set(cacheResult.get());

                        originalData.set(rawData);
                        cacheResult.set(result);

                        log.info("成功刷新{}配置缓存", name);
                    } catch (Exception e) {
                        log.error("刷新{}配置异常，保留旧缓存", name, e);
                    } finally {
                        log.info("刷新{}配置，耗时:{}", name, (System.currentTimeMillis() - startTime));
                    }
                }
            }
        } else {
            result = cacheResult.get();
        }

        return result;

    }
}
