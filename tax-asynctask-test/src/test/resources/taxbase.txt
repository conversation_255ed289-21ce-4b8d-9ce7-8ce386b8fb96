./taxbase/taxbase-msg/src/main/java/cn/com/servyou/taxbase/msg/consomer/DeclarationSyncForLoginMQConsumer.java:60:            String declareSyncFirstValue = redisCacheClient.getString(RedisUtils.getGroup(), declareSyncFirstKey);
./taxbase/taxbase-msg/src/main/java/cn/com/servyou/taxbase/msg/consomer/DeclarationSyncForLoginMQConsumer.java:66:                    redisCacheClient.setex(RedisUtils.getGroup(), declareSyncFirstKey,
./taxbase/taxbase-msg/src/main/java/cn/com/servyou/taxbase/msg/listener/TaxIdentificationDeclarationNotifyListener.java:69:            String value = (String) redisCacheClient.get(RedisUtils.getGroup(),key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:159:            redisCacheClient.put(RedisUtils.getGroup(), issueReqCacheKey, JSON.toJSONString(issueReq), ISSUE_EXPIRE_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:161:            redisCacheClient.remove(RedisUtils.getGroup(), issueResultCacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:179:            redisCacheClient.remove(RedisUtils.getGroup(), issueReqCacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:201:        String issueResult = Optional.ofNullable(redisCacheClient.get(RedisUtils.getGroup(), issueResultCacheKey)).map(Object::toString).orElse(null);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:206:        redisCacheClient.remove(RedisUtils.getGroup(), issueResultCacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:273:            redisCacheClient.put(RedisUtils.getGroup(), issueResultCacheKey, data, ISSUE_EXPIRE_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/certificate/AbstractTaxCertificateService.java:298:        String issueCache = Optional.ofNullable(redisCacheClient.get(RedisUtils.getGroup(), issueReqCacheKey)).map(Object::toString).orElse(null);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/taxpayer/impl/TaxpayerInfoServiceImpl.java:107:            boolean cacheExist = redisCacheClient.isExist(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/taxpayer/impl/TaxpayerInfoServiceImpl.java:138:            String result = redisCacheClient.setex(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.ONE_DAY_SECONDS, "NONE");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/taxpayer/impl/TaxpayerInfoServiceImpl.java:158:        String result = redisCacheClient.setex(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.ONE_DAY_SECONDS, "true");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/taxpayer/impl/TaxpayerInfoServiceImpl.java:408:            boolean cacheExist = redisCacheClient.isExist(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/taxpayer/impl/TaxpayerInfoServiceImpl.java:438:            String result = redisCacheClient.setex(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.ONE_DAY_SECONDS, "true");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/taxpayer/impl/TaxpayerInfoServiceImpl.java:451:        String result = redisCacheClient.setex(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.ONE_DAY_SECONDS, "true");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:41:        String result = REDIS_CACHE_CLIENT.set(REDIS_GROUP, cacheKey.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), seconds);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:53:        String result = REDIS_CACHE_CLIENT.set(REDIS_GROUP, cacheKey.getBytes(), value.getBytes(), "NX".getBytes(), "EX".getBytes(), seconds);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:63:        return REDIS_CACHE_CLIENT.isExist(REDIS_GROUP, cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:72:        REDIS_CACHE_CLIENT.del(REDIS_GROUP, cacheKey.getBytes());
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:76:        byte[] bytes = REDIS_CACHE_CLIENT.get(REDIS_GROUP, key.getBytes());
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:84:        REDIS_CACHE_CLIENT.put(REDIS_GROUP, new String(key.getBytes()), value.getBytes(), timeout);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/utils/RedisUtils.java:88:        REDIS_CACHE_CLIENT.remove(REDIS_GROUP, key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/hdxx/impl/JsHdxxCacheServiceImpl.java:70:        redisCacheClient.setex(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.ONE_HOUR_SECONDS, cacheVal);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/hdxx/impl/HdxxCacheServiceImpl.java:74:        redisCacheClient.setex(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS / 2, cacheVal);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/hdxx/impl/HdxxCacheServiceImpl.java:107:        String cacheVal = redisCacheClient.getString(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/hdxx/impl/HdxxCacheServiceImpl.java:111:            cacheVal = redisCacheClient.getString(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/config/DynamicCacheConfig.java:93:                Long cacheVersion = (Long) redisCacheClient.get(RedisUtils.getGroup(), getCheckEventCacheKey());
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/config/DynamicCacheConfig.java:195:        redisCacheClient.put(RedisUtils.getGroup(), getCheckEventCacheKey(), cacheVersion);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/config/DynamicCacheConfig.java:196:        redisCacheClient.expire(RedisUtils.getGroup(), getCheckEventCacheKey(), GlobalCacheConstants.ONE_MONTH_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/service/GlobalCommonService.java:38:        return redisCacheClient.isExist(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/service/TaxDeadLineComponent.java:30:        String deadLine = (String) redisCacheClient.get(RedisUtils.getGroup(), this.getCacheKey(period));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/service/TaxDeadLineComponent.java:48:        if (redisCacheClient.isExist(RedisUtils.getGroup(), cacheKey)
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/common/service/TaxDeadLineComponent.java:53:        redisCacheClient.put(RedisUtils.getGroup(), period, taxDeadline);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationServiceImpl.java:1209:            String value = (String)redisCacheClient.get(RedisUtils.getGroup(),String.format(GlobalCacheConstants.IDENTIFICATION_MANAGE_CHANGE_EVENT, info.getCustomerId(), period));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationServiceImpl.java:1215:            redisCacheClient.put(RedisUtils.getGroup(),String.format(GlobalCacheConstants.IDENTIFICATION_MANAGE_CHANGE_EVENT, info.getCustomerId(), period), value, 180);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:195:                redisCacheClient.remove(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:311:        redisCacheClient.remove(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:450:        //redisCacheClient.remove(REDIS_GROUP, TAX_TYPE_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:451:        //redisCacheClient.remove(REDIS_GROUP, TAXABLE_CERTIFICATE_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:491:        Map<Object, Object> mapAll = redisCacheClient.getMapAll(REDIS_GROUP, TAX_TYPE_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:501:        redisCacheClient.putToMap(REDIS_GROUP, TAX_TYPE_CONFIG_KEY, mapAll);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:522:        redisCacheClient.putToMap(REDIS_GROUP, TAX_TYPE_CONFIG_KEY, taxCode, taxTypeConfig);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:539:        redisCacheClient.putToMap(REDIS_GROUP, TAX_TYPE_CONFIG_KEY, mapAll);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:612:        Map<Object, Object> mapAll = redisCacheClient.getMapAll(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:618:                redisCacheClient.putToMap(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY, taxIdentificationConfigMap);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:636:        redisCacheClient.remove(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:658://        TaxTypeConfig taxTypeConfig = (TaxTypeConfig) redisCacheClient.getFromMap(REDIS_GROUP, TAX_TYPE_CONFIG_KEY, taxCode);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:982:        //redisCacheClient.remove(REDIS_GROUP, TAX_TYPE_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:984:        redisCacheClient.remove(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:986:        //redisCacheClient.remove(REDIS_GROUP, TAXABLE_CERTIFICATE_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1032:        redisCacheClient.remove(REDIS_GROUP, TAX_IDENTIFICATION_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1210:        List<TaxKeywordConfig> taxKeywordConfigs = (List<TaxKeywordConfig>) redisCacheClient.get(REDIS_GROUP, cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1220:        redisCacheClient.put(REDIS_GROUP, cacheKey, taxKeywordConfigs);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1273:        redisCacheClient.remove(REDIS_GROUP, cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1281:        redisCacheClient.remove(REDIS_GROUP, getTaxKeywordKey(taxKeywordConfigDTO.getAreaCode(), KeyWordTypeConfigEnum.getByCode(taxKeywordConfigDTO.getTypeFlag())));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1290:        List<SpecialTaxCreationConfig> configs = (List<SpecialTaxCreationConfig>) redisCacheClient.get(REDIS_GROUP, SPECIAL_TAX_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1294:                redisCacheClient.put(REDIS_GROUP, SPECIAL_TAX_CONFIG_KEY, configs);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1341:            redisCacheClient.remove(REDIS_GROUP, SPECIAL_TAX_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/impl/TaxIdentificationConfigRootServiceImpl.java:1351:            redisCacheClient.remove(REDIS_GROUP, SPECIAL_TAX_CONFIG_KEY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:44:                redisCacheClient.put(REDIS_GROUP, String.format(CRAWL_IDENTIFICATION_PLACEHOLDER_KEY, mate.getMessageId()), request, EXPIRE);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:47:            redisCacheClient.put(REDIS_GROUP, String.format(CRAWL_IDENTIFICATION_PLACEHOLDER_KEY, taskId), request, EXPIRE);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:52:        CrawlIdentificationResult crawlIdentificationResult = (CrawlIdentificationResult) redisCacheClient.get(REDIS_GROUP, String.format(CRAWL_IDENTIFICATION_PLACEHOLDER_KEY, taskId));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:57:        CrawlIdentificationResult crawlIdentificationResult = (CrawlIdentificationResult) redisCacheClient.get(REDIS_GROUP, String.format(CRAWL_IDENTIFICATION_PLACEHOLDER_KEY, taskId));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:71:        boolean exist = redisCacheClient.isExist(REDIS_GROUP, getAutoUpdateKey(customerId, time));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:76:        redisCacheClient.put(REDIS_GROUP, getAutoUpdateKey(customerId, time), "1");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:77:        redisCacheClient.expire(REDIS_GROUP, getAutoUpdateKey(customerId,time), calcTime().intValue());
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationHelper.java:81:        redisCacheClient.remove(REDIS_GROUP, getAutoUpdateKey(customerId, time));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationConfigHelper.java:58:        redisCacheClient.putString(RedisUtils.getGroup(), configType.name(), String.valueOf(startValue));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/identification/helper/TaxIdentificationConfigHelper.java:62:        return redisCacheClient.incr(RedisUtils.getGroup(), key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/SxLocalTaxImpl.java:54:        Object cache = redisCacheClient.get(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/SxLocalTaxImpl.java:76:        redisCacheClient.put(RedisUtils.getGroup(), cacheKey, result, 60);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/declareResult/RemoveDeclarationResultProcess.java:70:            boolean exist = redisCacheClient.isExist(RedisUtils.getGroup(), key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/declareResult/RemoveDeclarationResultProcess.java:72:                redisCacheClient.put(RedisUtils.getGroup(), key, 1, expires);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/declareResult/RemoveDeclarationResultProcess.java:91:        boolean exist = redisCacheClient.isExist(RedisUtils.getGroup(), key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/declareResult/RemoveDeclarationResultProcess.java:93:            redisCacheClient.put(RedisUtils.getGroup(), key, 1, expires);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/declareResult/RemoveDeclarationResultProcess.java:106:        boolean exist = redisCacheClient.isExist(RedisUtils.getGroup(), key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/declareResult/RemoveDeclarationResultProcess.java:108:            redisCacheClient.put(RedisUtils.getGroup(), key, 1, expires);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:104:        redisCacheClient.remove(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:122:        redisCacheClient.remove(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:141:        String cacheValue = redisCacheClient.getString(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:148:                redisCacheClient.setex(RedisUtils.getGroup(), ossKey, GlobalCacheConstants.ONE_MONTH_SECONDS, JsonUtils.toJsonString(rules));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:348:                redisCacheClient.remove(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:351:                redisCacheClient.remove(RedisUtils.getGroup(), ossTaxCodeInfoKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:354:                redisCacheClient.remove(RedisUtils.getGroup(), ossKeyWordKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:361:            redisCacheClient.remove(RedisUtils.getGroup(), "tx:szlx");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:362:            redisCacheClient.remove(RedisUtils.getGroup(), "tx:tssz");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/WebActionRuleServiceImpl.java:363:            redisCacheClient.remove(RedisUtils.getGroup(), "tx:sfz");
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/NormalSyncService.java:117:        String oldCache = redisCacheClient.getString(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/NormalSyncService.java:128:        redisCacheClient.putString(RedisUtils.getGroup(), cacheKey, cache);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/NormalSyncService.java:129:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, interval);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/NormalSyncService.java:192:            redisCacheClient.put(RedisUtils.getGroup(), key, "true", 60);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/MiscellaneousServiceImpl.java:46:        if (redisCacheClient.isExist(RedisUtils.getGroup(), freshKey)) {
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/impl/MiscellaneousServiceImpl.java:55:        redisCacheClient.put(RedisUtils.getGroup(), freshKey, "ok", DateUtil.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/listener/AsyncSyncStatusListener.java:160:        if (redisCacheClient.isExist(RedisUtils.getGroup(), notifyKey)) {
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/listener/AsyncSyncStatusListener.java:164:        redisCacheClient.put(RedisUtils.getGroup(), notifyKey, "ok", DateUtil.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/listener/AsyncSyncStatusListener.java:386://                redisCacheClient.setex(RedisUtils.getGroup(), declareSyncFirstKey, Long.valueOf(cn.hutool.core.date.DateUtil.between(new Date(), cn.hutool.core.date.DateUtil.endOfDay(new Date()), DateUnit.SECOND)).intValue(), declareSyncFirstKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/listener/SinpleSyncStatusListener.java:125:        if (redisCacheClient.isExist(RedisUtils.getGroup(), notifyKey)) {
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/listener/SinpleSyncStatusListener.java:129:        redisCacheClient.put(RedisUtils.getGroup(), notifyKey, "ok", DateUtil.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/listener/SinpleSyncStatusListener.java:283://                redisCacheClient.setex(RedisUtils.getGroup(), declareSyncFirstKey, Long.valueOf(cn.hutool.core.date.DateUtil.between(new Date(), cn.hutool.core.date.DateUtil.endOfDay(new Date()), DateUnit.SECOND)).intValue(), declareSyncFirstKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:802:            redisCacheClient.hset(RedisUtils.getGroup(), cacheKey, message, StringUtils.EMPTY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:803:            redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:817:        Map<String, String> resultMap = redisCacheClient.hgetAll(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:831:        if (redisCacheClient.isExist(RedisUtils.getGroup(), cacheKey)) {
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:832:            redisCacheClient.hset(RedisUtils.getGroup(), cacheKey, messageId, StringUtils.EMPTY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:834:            redisCacheClient.hset(RedisUtils.getGroup(), cacheKey, messageId, StringUtils.EMPTY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:835:            redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:844:        Map<String, String> map = redisCacheClient.hgetAll(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:857:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:875:            redisCacheClient.put(RedisUtils.getGroup(), cacheKey, result, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:886:        if (!redisCacheClient.isExist(RedisUtils.getGroup(), cacheKey)) {
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:890:        Object result = redisCacheClient.get(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:891:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:920:        return redisCacheClient.hexists(RedisUtils.getGroup(), cacheKey, messageId);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1055:        redisCacheClient.put(RedisUtils.getGroup(), initMetaKey, obtainMate, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1065:        return (AsyncObtainMate) redisCacheClient.get(RedisUtils.getGroup(), initMetaKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1075:        redisCacheClient.remove(RedisUtils.getGroup(), initMetaKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1084:        redisCacheClient.incrBy(RedisUtils.getGroup(), cacheKey, number);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1085:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1094:        return redisCacheClient.incrBy(RedisUtils.getGroup(), cacheKey, -1);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1103:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1112:        redisCacheClient.put(RedisUtils.getGroup(), cacheKey, messageId, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1121:        return (String) redisCacheClient.get(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1130:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1153:        redisCacheClient.hset(RedisUtils.getGroup(), cacheKey, key, JsonUtils.toJsonString(obj));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1154:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1163:        return redisCacheClient.hgetAll(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1172:        redisCacheClient.sadd(RedisUtils.getGroup(), cacheKey, JsonUtils.toJsonString(mate));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1173:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1182:        Set<String> members = redisCacheClient.smembers(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1196:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1205:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1215:        redisCacheClient.put(RedisUtils.getGroup(), initMetaKey, JsonUtils.toJsonString(request), DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/CommonService.java:1225:        redisCacheClient.remove(RedisUtils.getGroup(), initMetaKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/v2/process/sync/DeclartionResultHandler.java:534:        redisCacheClient.setex(RedisUtils.getGroup(), key, 300, JSON.toJSONString(speUpdateMap));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/v2/process/sync/DeclartionResultHandler.java:539:        String result = redisCacheClient.getString(RedisUtils.getGroup(), key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/v2/process/sync/DeclartionResultHandler.java:546:        redisCacheClient.remove(RedisUtils.getGroup(), key);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/LocalCommonService.java:379:        redisCacheClient.remove(RedisUtils.getGroup(), commonService.buildMessageIdCacheKey(customerId, period, mode));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:34:        redisCacheClient.put(RedisUtils.getGroup(), this.getSyncDataCarrierCacheKey(messageId), carrier, GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:37:        redisCacheClient.hset(RedisUtils.getGroup(), carrierCacheHashKey, SbztSyncDataCarrier.DECLARE, StringUtils.EMPTY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:38:        redisCacheClient.hset(RedisUtils.getGroup(), carrierCacheHashKey, SbztSyncDataCarrier.PAYMENT, StringUtils.EMPTY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:39:        redisCacheClient.hset(RedisUtils.getGroup(), carrierCacheHashKey, SbztSyncDataCarrier.UN_PAYMENT, StringUtils.EMPTY);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:41:        redisCacheClient.expire(RedisUtils.getGroup(), carrierCacheHashKey, GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:48:        redisCacheClient.remove(RedisUtils.getGroup(), this.getSyncDataCarrierCacheKey(messageId));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:55:        if (!redisCacheClient.isExist(RedisUtils.getGroup(), this.getSyncDataCarrierCacheKey(messageId))) {
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:59:        SbztSyncDataCarrier carrier = (SbztSyncDataCarrier) redisCacheClient.get(RedisUtils.getGroup(), this.getSyncDataCarrierCacheKey(messageId));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:62:        Map<String, String> map = redisCacheClient.hgetAll(RedisUtils.getGroup(), carrierCacheHashKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:90:        Map<String, String> map = redisCacheClient.hgetAll(RedisUtils.getGroup(), carrierCacheHashKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:95:                redisCacheClient.hset(RedisUtils.getGroup(), carrierCacheHashKey, SbztSyncDataCarrier.DECLARE, JsonUtils.toJsonString(result));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:99:                redisCacheClient.hset(RedisUtils.getGroup(), carrierCacheHashKey, SbztSyncDataCarrier.PAYMENT, JsonUtils.toJsonString(result));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:103:                redisCacheClient.hset(RedisUtils.getGroup(), carrierCacheHashKey, SbztSyncDataCarrier.UN_PAYMENT, JsonUtils.toJsonString(result));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:116:        redisCacheClient.putString(RedisUtils.getGroup(), cacheKey, messageId);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:117:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:124:        redisCacheClient.remove(RedisUtils.getGroup(), this.getSyncDeclareCacheKey(declareNo));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:131:        return redisCacheClient.getString(RedisUtils.getGroup(), this.getSyncDeclareCacheKey(declareNo));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:139:        redisCacheClient.putString(RedisUtils.getGroup(), cacheKey, messageId);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:140:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:147:        redisCacheClient.remove(RedisUtils.getGroup(), this.getSyncPaymentCacheKey(paymentNo));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:154:        return redisCacheClient.getString(RedisUtils.getGroup(), this.getSyncPaymentCacheKey(declareNo));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:162:        redisCacheClient.putString(RedisUtils.getGroup(), cacheKey, messageId);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:163:        redisCacheClient.expire(RedisUtils.getGroup(), cacheKey, GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:173:        redisCacheClient.remove(RedisUtils.getGroup(), this.getSyncUnPaymentCacheKey(unPaymentNo));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:180:        return redisCacheClient.getString(RedisUtils.getGroup(), this.getSyncUnPaymentCacheKey(declareNo));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:187:        redisCacheClient.incrBy(RedisUtils.getGroup(), this.getSyncTaskCountCacheKey(messageId), count);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:188:        redisCacheClient.expire(RedisUtils.getGroup(), this.getSyncTaskCountCacheKey(messageId), GlobalCacheConstants.DEFAULT_TIMEOUT_SECONDS);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:195:        return redisCacheClient.incrBy(RedisUtils.getGroup(), this.getSyncTaskCountCacheKey(messageId), 1);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:202:        return redisCacheClient.isExist(RedisUtils.getGroup(), this.getSyncTaskCountCacheKey(messageId));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/SbztCommonService.java:209:        redisCacheClient.remove(RedisUtils.getGroup(), this.getSyncTaskCountCacheKey(messageId));
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/TaxCodeInfoService.java:100:        List<TaxCodeInfo> list = (List<TaxCodeInfo>) redisCacheClient.get(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/TaxCodeInfoService.java:109:        redisCacheClient.put(RedisUtils.getGroup(), cacheKey, taxCodeInfos);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/TaxCodeInfoService.java:158:        redisCacheClient.remove(RedisUtils.getGroup(), cacheKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/StateFilterRuleService.java:32:        redisCacheClient.remove(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/StateFilterRuleService.java:44:        redisCacheClient.remove(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/StateFilterRuleService.java:54:        String cacheValue = redisCacheClient.getString(RedisUtils.getGroup(), ossKey);
./taxbase/taxbase-core/src/main/java/cn/com/servyou/taxbase/core/declaration/StateFilterRuleService.java:61:                redisCacheClient.setex(RedisUtils.getGroup(), ossKey, GlobalCacheConstants.ONE_MONTH_SECONDS, JsonUtils.toJsonString(rule));
