./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/fr/jrqy/TaxFrJrqyAssetDebitExcuteReportServiceImpl.java:285:                    toolFacade.clearOssFile(String.format(FR_FILE_PATH + "%s/%s", customerId, declarationState.getId()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/fr/TaxFrYbqyAssetDebitExcuteReportServiceImpl.java:253:                    toolFacade.clearOssFile(String.format(FR_FILE_PATH + "%s/%s", customerId, declarationState.getId()));
./taxcore/xqy-taxcore-test/src/test/java/cn/com/servyou/xqy/taxcore/facade/tool/ToolFacadeTest.java:25:        ListResult<String> result = toolFacade.queryByRegex("cn.com.servyou.xqy.taxcore.dao.basic.taxcode.ITaxNtTaxCodeInfoDao*");
./taxcore/xqy-taxcore-test/src/test/java/cn/com/servyou/xqy/taxcore/facade/tool/ToolFacadeTest.java:32:        SingleResult<String> result = toolFacade.queryBykey("BASIC_DICTIONARY_4403_host_CODE");
./taxcore/xqy-taxcore-test/src/test/java/cn/com/servyou/xqy/taxcore/facade/tool/ToolFacadeTest.java:43:        SingleResult<Boolean> result = toolFacade.deleteByRegex("cn.com.servyou.xqy.taxcore.dao.basic.taxcode.ITaxNtTaxCodeInfoDao*");
./taxcore/xqy-taxcore-test/src/test/java/cn/com/servyou/xqy/taxcore/facade/tool/ToolFacadeTest.java:50:        SingleResult<Long> result = toolFacade.expiresBykey("cn.com.servyou.xqy.taxcore.dao.basic.taxcode.ITaxNtTaxCodeInfoDao", 1000);
./taxcore/xqy-taxcore-test/src/test/java/cn/com/servyou/xqy/taxcore/facade/tool/ToolFacadeTest.java:57:        toolFacade.updateBykey("123","123",200,"A");
./taxcore/xqy-taxcore-test/src/test/java/cn/com/servyou/xqy/taxcore/facade/tool/ToolFacadeTest.java:61:        toolFacade.updateSimpleTypeByKey("123","123",200,"A");
