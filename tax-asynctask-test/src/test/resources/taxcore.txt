./taxcore/xqy-taxcore-integration/src/main/java/cn/com/servyou/xqy/taxcore/integration/gateway/impl/TaxGatewayClientImpl.java:681:            Object yjOptimize = redisCacheClient.get(REDIS_GROUP, String.format(SATISFIED_YJ_OPTIMIZE_REDIS, request.getCustomerId()));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/temp/TempTaskImpl.java:120:        redisCacheClient.remove(REDIS_GROUP, freshKey);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/correct/AllowCorrectFacadeImpl.java:96:                        cacheClient.put(REDIS_GROUP, String.format(ALLOW_CORRECT_KEY,
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/ybnsrzc/YbnsrZcFacadeImpl.java:425:            redisCacheClient.put(REDIS_GROUP, String.format(SATISFIED_JJDJ_OPTIMIZE_REDIS, customerId), JSON.toJSONString(new JjdjSaveInfoDTO(xz, bz)), 24 * 60 * 60);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/js/JsTaxFacadeImpl.java:2616:        if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/js/JsTaxFacadeImpl.java:2646:                    if (!redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/js/JsTaxFacadeImpl.java:2934:        redisCacheClient.put(REDIS_GROUP, key, "ok", DateUtils.getNumberBetweenNowAndUnknownMorning(7, ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/TaxFacadeImpl.java:657:                if (!StringUtils.equals("OK", redisCacheClient.set(REDIS_GROUP, lockKey.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 60))) {
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/TaxFacadeImpl.java:671:                    redisCacheClient.remove(REDIS_GROUP, lockKey);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/TaxFacadeImpl.java:2368:                    redisCacheClient.put(REDIS_GROUP, String.format("%s:%s:%s", DECLARE_DATA, declareRequestJOs.getCustomerId(), declareRequestJOs.getTaxCode()), JSON.toJSONString(declareRequestJOs), 30 * 24 * 60 * 60);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/TaxFacadeImpl.java:3264:                    redisCacheClient.put(REDIS_GROUP, cacheKey, xzqk, 600);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/SimpleDeclareFacadeImpl.java:89:                        redisCacheClient.set(REDIS_GROUP, key.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), DateUtils.getNumberBetweenNowAndNextMonthMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/HybridTaxService.java:1645:        String checkFdWhiteValue = redisCacheClient.getString(REDIS_GROUP, String.format(checkFdWhiteKey, customerId));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/HybridTaxService.java:1658:            redisCacheClient.put(REDIS_GROUP, String.format(checkFdWhiteKey, customerId), "Y", (int) ChronoUnit.SECONDS.between(now, endDateTime));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/HybridTaxService.java:2596:            String result = redisCacheClient.set("first", genKey(customerId, taxTypeMap, declareTypeEnum), UUIDUtil.generateUuid().getBytes(), "NX".getBytes(), "PX".getBytes(), 3 * 60 * 1000);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/HybridTaxService.java:3158:            String cacheVal = Optional.ofNullable(redisCacheClient.get(REDIS_GROUP, String.format("%s:%s:%s", DECLARE_DATA, customerId, taxTypeEnum.getCode()))).orElse("").toString();
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/HybridTaxService.java:3283:            String bjJjdjXz = (String) redisCacheClient.get(REDIS_GROUP, String.format(SATISFIED_JJDJ_OPTIMIZE_REDIS, customerId));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/HybridTaxService.java:4135:        redisCacheClient.del("first", String.format(DECLARE_LOCK, customerId).getBytes());
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/ReportSelectFacadeImpl.java:308:            Object systemInit = redisCacheClient.get(REDIS_GROUP, String.format("%s:%s", "selectSetting", customerId));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/ReportSelectFacadeImpl.java:350:                    redisCacheClient.remove(REDIS_GROUP, String.format("%s:%s","selectSetting", customerId));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/GatewayCompareService.java:316:                redisCacheClient.put(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRM_REDIS_KEY, declarationState.getCustomerId(), declarationState.getPeriod()), "Y", 60 * 60 * 24 * 31);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declare/GatewayCompareService.java:589://            redisCacheClient.put(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRM_REDIS_KEY, declarationState.getCustomerId(), declarationState.getPeriod()), compareDTO.getJxfpConfirm(), 60 * 60 * 24 * 31);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/xtax/hdxx/TaxHdxxFacadeImpl.java:633:        redisCacheClient.put(REDIS_GROUP, String.format(DOWNLOADED_MARK, customerId, period, taxCode, appId), "ok", DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/xtax/hdxx/TaxHdxxFacadeImpl.java:637:        return redisCacheClient.isExist(REDIS_GROUP, String.format(DOWNLOADED_MARK, customerId, period, taxCode, appId));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/jmxz/JmxzCacheFacadeImpl.java:171:                    redisCacheClient.remove(REDIS_GROUP, freshKey);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/jmxz/JmxzCacheFacadeImpl.java:181:        if (redisCacheClient.isExist(REDIS_GROUP, freshKey)) {
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/jmxz/JmxzCacheFacadeImpl.java:184:            redisCacheClient.put(REDIS_GROUP, freshKey, "ok", DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/flzl/FlzlQueryFacadeImpl.java:129:                    redisCacheClient.remove(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/flzl/FlzlQueryFacadeImpl.java:159:        String cacheVal = (String)redisCacheClient.get(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/flzl/FlzlQueryFacadeImpl.java:168:        redisCacheClient.put(REDIS_GROUP, cacheKey, JSONArray.toJSONString(list), flzlListTimeout);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/task/DeclareTaskService.java:487:            String result = redisCacheClient.set("first", String.format(DECLARATION_TASK_ADD_LOCK, customerId, period).getBytes(), UUIDUtil.generateUuid().getBytes(), "NX".getBytes(), "PX".getBytes(), 3 * 60 * 1000);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/task/DeclareTaskService.java:514:                redisCacheClient.del("first", String.format(DECLARATION_TASK_ADD_LOCK, customerId, period).getBytes());
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/zzsyj/ValueAddedTaxPrepaymentFacadeImpl.java:190:            redisCacheClient.put(REDIS_GROUP, String.format(SATISFIED_YJ_OPTIMIZE_REDIS, customerId), YesOrNoEnum.YES.getCode(), 24 * 60 * 60);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/zzsyj/ValueAddedTaxPrepaymentFacadeImpl.java:198:            redisCacheClient.remove(REDIS_GROUP, String.format(SATISFIED_YJ_OPTIMIZE_REDIS, customerId));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/compare/FdKeyDataCompareFacadeImpl.java:47:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "1", 60 * 5);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/manage/CalculatorManagerFacadeImpl.java:581:                cacheClient.remove(redisGroup, key);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/tool/ToolFacadeImpl.java:71:                Object obj = redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/tool/ToolFacadeImpl.java:88:                Object obj = redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/tool/ToolFacadeImpl.java:112:                    result.setEntity(redisCacheClient.put(REDIS_GROUP, key, JSON.toJavaObject(JSON.parseObject(value), Class.forName(className)), timeout));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/tool/ToolFacadeImpl.java:132:                    result.setEntity(redisCacheClient.put(REDIS_GROUP, key, value, timeout));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/tool/ToolFacadeImpl.java:164:                    result.setEntity(redisCacheClient.put(REDIS_GROUP, key, objValue, timeout));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/tool/ToolFacadeImpl.java:199:                result.setEntity(redisCacheClient.expire(REDIS_GROUP, key, timeout));
./taxcore/xqy-taxcore-facadeimpl/src/main/java/cn/com/servyou/xqy/taxcore/facadeimpl/declaration/DeclarationInfoFacadeImpl.java:759:                                        String fdUserSaved = (String) redisCacheClient.get(REDIS_GROUP, String.format(FD_USER_SAVED, taxNo));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:334:        if (cacheClient.isExist(REDIS_GROUP, cacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:335:            cancelQueryResultDetailList = JSON.parseArray((String) cacheClient.get(REDIS_GROUP, cacheKey), Sb00301QueryZhSbxxResponseDTO.class);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:352:            cacheClient.put(REDIS_GROUP, cacheKey, JSON.toJSONString(cancelQueryResultDetailList), second);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:431:            cacheClient.remove(REDIS_GROUP, String.format(FD_UNPAIDINFODETAIL_TAXNO, taxNo));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:432:            cacheClient.remove(REDIS_GROUP, String.format(FD_CANCELQUERYXX_CODE_TAXNO, taxCodeType, taxNo, declPeriod));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:450:            cacheClient.remove(REDIS_GROUP, String.format(UNPAIDINFODETAILSBF_TAXNO, taxNo));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:452:            cacheClient.remove(REDIS_GROUP, String.format(UNPAIDINFODETAIL_TAXNO, taxNo));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:457:        if (!cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:460:        Map<String, Map<String, Object>> cacheMap = (Map<String, Map<String, Object>>) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:468:        cacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:531:        if (!cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:534:        Map<String, Map<String, Object>> cacheMap = (Map<String, Map<String, Object>>) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:570:        if (!cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:573:            cacheMap = (Map<String, Map<String, Object>>) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/cancel/impl/DeclareCancelServiceImpl.java:577:        cacheClient.put(REDIS_GROUP, key, cacheMap, 3600);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:111:        redisCacheClient.putToMap(REDIS_GROUP, String.format(FD_IMPORT_RESULT_KEY, excelId), map);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:112:        redisCacheClient.expire(REDIS_GROUP, String.format(FD_IMPORT_RESULT_KEY, excelId), FD_WHITE_EXPIRE);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:113:        redisCacheClient.putToMap(REDIS_GROUP, String.format(FD_IMPORT_COUNT_KEY, excelId), splitCountMap);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:114:        redisCacheClient.expire(REDIS_GROUP, String.format(FD_IMPORT_COUNT_KEY, excelId), FD_WHITE_EXPIRE);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:141:            redisCacheClient.putToMap(REDIS_GROUP, String.format(FD_IMPORT_COUNT_KEY, fdImportSource.getExcelId()), fdImportSource.getPath(), taxNos.size());
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:142:            redisCacheClient.putToMap(REDIS_GROUP, String.format(FD_IMPORT_RESULT_KEY, fdImportSource.getExcelId()), fdImportSource.getPath(), "Y");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:144:            redisCacheClient.putToMap(REDIS_GROUP, String.format(FD_IMPORT_RESULT_KEY, fdImportSource.getExcelId()), fdImportSource.getPath(), "N");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:164:            Map<Object, Object> mapAll = redisCacheClient.getMapAll(REDIS_GROUP, String.format(FD_IMPORT_RESULT_KEY, fdImportSource.getExcelId()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fd/FdWhiteImportService.java:167:                Map<Object, Object> countMap = redisCacheClient.getMapAll(REDIS_GROUP, String.format(FD_IMPORT_COUNT_KEY, fdImportSource.getExcelId()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:129:        String code = (String) cacheClient.get(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:136:            cacheClient.put(redisGroup, redisDictionGroupKey, code, dicDeadTime);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:173:        Map<String, String> dictionaryMap = (Map<String, String>) cacheClient.get(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:181:            cacheClient.put(redisGroup, redisDictionGroupKey, dictionaryMap, BASIC_DICTIONARY_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:207:            cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:209:            cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:268:                cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:270:                cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:284:        cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:286:        cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:331:        cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:333:        cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:340:        cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:342:        cacheClient.remove(redisGroup, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:366:        String menusContent = (String) redisCacheClient.get(REDIS_MENU_LINK_GROUP, MENU_LINK_KEY);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:371:        redisCacheClient.remove(REDIS_MENU_LINK_GROUP, MENU_LINK_KEY);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/DictionaryBasicServiceImpl.java:480:                dtoList.forEach(dic -> cacheClient.remove(redisGroup, String.format(BASIC_DICTIONARY_AREACODE_GROUP_CODE, areaCode, dic.getGroup())));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/ReportItemServiceImpl.java:42:        List<ReportItemDO> taxReportItemList = (List<ReportItemDO>) cacheClient.get(REDIS_GROUP, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/ReportItemServiceImpl.java:48:            cacheClient.put(REDIS_GROUP, redisDictionGroupKey, taxReportItemList, TAX_REDIS_REPORT_ITEM_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/ReportItemServiceImpl.java:60:        List<ReportItemDO> taxReportItemList = (List<ReportItemDO>) cacheClient.get(REDIS_GROUP, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/ReportItemServiceImpl.java:66:            cacheClient.put(REDIS_GROUP, redisDictionGroupKey, taxReportItemList, TAX_REDIS_REPORT_ITEM_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxCtCorporateTaxSelectedBodyService.java:59:        if (!cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxCtCorporateTaxSelectedBodyService.java:64:                cacheClient.put(REDIS_GROUP, key, ctCorporateTaxSelectedBodyDOList, LOST_TIME_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxCtCorporateTaxSelectedBodyService.java:68:            ctCorporateTaxSelectedBodyDOList = (List<TaxCtCorporateTaxSelectedBodyDO>) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxUnSupportOperServiceImpl.java:369:        taxCodeList.removeIf(item -> redisCacheClient.sismember(REDIS_GROUP, key, item));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxUnSupportOperServiceImpl.java:387:        redisCacheClient.sadd(REDIS_GROUP, key, taxCodeList.toArray(new String[0]));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxUnSupportOperServiceImpl.java:805:        if (!cacheClient.isExist(REDIS_GROUP, redisSelectedGroupKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxUnSupportOperServiceImpl.java:808:                cacheClient.put(REDIS_GROUP, redisSelectedGroupKey, tgc, LOST_TIME);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settings/impl/TaxUnSupportOperServiceImpl.java:811:            tgc = (String) cacheClient.get(REDIS_GROUP, redisSelectedGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settlement/impl/FinalSettlementRuleServiceImpl.java:80:        cacheClient.put(REDIS_GROUP, userMatchedDataKey, accountAndTaxRelationDatas, FinalTaxConstant.USER_RULE_DATA_TTL_SECOND);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settlement/impl/FinalSettlementRuleServiceImpl.java:293:        List<AccountAndTaxRelationDataDTO> accountAndTaxRelationsFromCache = (List<AccountAndTaxRelationDataDTO>) cacheClient.get(REDIS_GROUP, userExcelDataKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settlement/impl/FinalSettlementDataServiceImpl.java:272:            cacheClient.put(REDIS_GROUP, userExcelDataKey, excleDatas, FinalTaxConstant.USER_EXCEL_DATA_TTL_SECOND);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settlement/impl/FinalSettlementDataServiceImpl.java:448:        String[][] excleDatas = (String[][]) cacheClient.get(REDIS_GROUP, userExcelDataKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settlement/impl/FinalSettlementDataServiceImpl.java:576:            cacheClient.remove(REDIS_GROUP, userExcelDataKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/settlement/impl/FinalSettlementDataServiceImpl.java:577:            cacheClient.remove(REDIS_GROUP, userMatchedDataKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialServiceImpl.java:156:        if (!StringUtils.equals("OK", redisCacheClient.set(REDIS_GROUP, lockKey.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), expireTime()))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialServiceImpl.java:208:            redisCacheClient.del(REDIS_GROUP, lockKey.getBytes());
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/ZjTaxInitialServiceImpl.java:181:        if (!redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/ZjTaxInitialServiceImpl.java:182:            redisCacheClient.put(REDIS_GROUP, key, JSON.toJSONString(request), 60 * 10); // 缓存10分钟
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/SzTaxInitialServiceImpl.java:206:            if (!redisCacheClient.isExist(REDIS_GROUP, key) || !StringUtils.equals((String) redisCacheClient.get(REDIS_GROUP, key), md5)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/SzTaxInitialServiceImpl.java:207:                redisCacheClient.put(REDIS_GROUP, key, md5, 2592000);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialServiceDefaultImpl.java:390:            if (!cacheClient.isExist(REDIS_GROUP, key) || StringUtils.isNotBlank(TraceTags.getTagValue("inRetry"))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialServiceDefaultImpl.java:391:                cacheClient.put(REDIS_GROUP, key, true, 60);//设置过期时间为1分钟
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialServiceDefaultImpl.java:397:            cacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialSbfServiceImpl.java:94:        String cacheVal = Optional.ofNullable(redisCacheClient.get(REDIS_GROUP, cacheKey)).orElse("").toString();
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/TaxInitialSbfServiceImpl.java:143:        redisCacheClient.put(REDIS_GROUP, cacheKey, daqResult.getPayloadFromPtfc(), ttlSeconds);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/JsTaxInitialServiceImpl.java:391:        if (redisCacheClient.isExist(REDIS_GROUP, keyDown) && StringUtils.isBlank(TraceTags.getTagValue("inRetry"))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/JsTaxInitialServiceImpl.java:395:            redisCacheClient.put(REDIS_GROUP, keyDown, true, 60);//设置过期时间为1分钟
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/JsTaxInitialServiceImpl.java:400:                redisCacheClient.remove(REDIS_GROUP, keyDown);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/impl/JsTaxInitialServiceImpl.java:412:            redisCacheClient.remove(REDIS_GROUP, keyDown);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/observer/TaxStateDeclHdxxChangedObserer.java:155:        String deadLine = (String) cacheClient.get(REDIS_GROUP, String.format(DEAD_LINE_KEY, hdxxDto.getPeriod()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/observer/TaxStateDeclHdxxChangedObserer.java:168:            cacheClient.put(REDIS_GROUP, String.format(DEAD_LINE_KEY, hdxxDto.getPeriod()), optionalDeadLine.get());
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:257:            if (!redisCacheClient.isExist(REDIS_GROUP, key) || StringUtils.isNotBlank(TraceTags.getTagValue("inRetry"))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:258:                redisCacheClient.put(REDIS_GROUP, key, true, 60);//设置过期时间为1分钟
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:265:            redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:348:        if (redisCacheClient.isExist(REDIS_GROUP, String.format(AUTO_DOWNLOAD_INITIAL_LOCK, taxNo, period))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:359:        return "OK".equals(redisCacheClient.set(REDIS_GROUP, String.format(AUTO_DOWNLOAD_INITIAL_LOCK, taxNo, period).getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 10));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:363:        redisCacheClient.remove(REDIS_GROUP, String.format(AUTO_DOWNLOAD_INITIAL_LOCK, taxNo, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:374:            if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:377:            redisCacheClient.set(REDIS_GROUP, key.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 10);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:381:                redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:733:        redisCacheClient.remove(REDIS_GROUP, String.format(SAVE_FLHD_LOCK_KEY, taxNo, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/initial/abs/AbsTaxInitialWorkService.java:737:        return "OK".equals(redisCacheClient.set(REDIS_GROUP, String.format(SAVE_FLHD_LOCK_KEY, taxNo, period).getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 10));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/config/impl/ParamConfigServiceImpl.java:227:            redisCacheClient.putString(REDIS_GROUP, FD_WHITE_CONFIG, configValue);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/filter/LocalTaxFilterByProperty.java:111:        if (redisCacheClient.isExist(REDIS_GROUP, redisCacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/filter/LocalTaxFilterByProperty.java:112:            return redisCacheClient.get(REDIS_GROUP, redisCacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/filter/LocalTaxFilterByProperty.java:114:            if (redisCacheClient.isExist(REDIS_GROUP, cnRedisCacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/filter/LocalTaxFilterByProperty.java:115:                return redisCacheClient.get(REDIS_GROUP, cnRedisCacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/filter/LocalTaxFilterByProperty.java:128:                redisCacheClient.put(REDIS_GROUP, cnRedisCacheKey, JSON.toJavaObject(JSON.parseObject(filterJson), ruleClass));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/filter/LocalTaxFilterByProperty.java:134:        redisCacheClient.put(REDIS_GROUP, redisCacheKey, JSON.toJavaObject(JSON.parseObject(filterJson), ruleClass));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/GdLocalTaxServiceImpl.java:356:        String url = (String) cacheClient.get(REDIS_GROUP, String.format(CT_URL_CACHE, customerId, taxNo, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/localtax/impl/GdLocalTaxServiceImpl.java:373:        cacheClient.put(REDIS_GROUP, String.format(CT_URL_CACHE, customerId, taxNo, period), url, LOCAL_CACHE_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/taxpayer/impl/TaxpayerServiceImpl.java:483:        if (StringUtils.isEmpty(djxh) || redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/taxpayer/impl/TaxpayerServiceImpl.java:495:            redisCacheClient.set(REDIS_GROUP, key.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(),
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/taxpayer/impl/TaxpayerServiceImpl.java:549:                redisCacheClient.put(REDIS_GROUP, String.format(CacheKeyConstant.SBZT_TAXPAYER_REFRESH_META_KEY,
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/taxpayer/impl/TaxpayerServiceImpl.java:562:        Object cache = redisCacheClient.get(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/taxpayer/impl/TaxpayerServiceImpl.java:575:        redisCacheClient.remove(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/deadline/impl/TaxDeadLineServiceImpl.java:306:        redisCacheClient.remove(REDIS_GROUP, String.format(TAX_DEADLINE_CACHE_KEY, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/deadline/handler/TaxDeadLineHandler.java:138:        String data = (String) redisCacheClient.get(REDIS_GROUP, String.format(TAX_DEADLINE_CACHE_KEY, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/deadline/handler/TaxDeadLineHandler.java:142:            redisCacheClient.put(REDIS_GROUP, String.format(TAX_DEADLINE_CACHE_KEY, period), data, REMOVE_TIME);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/deadline/handler/TaxDeadLineHandler.java:222:        redisCacheClient.remove(REDIS_GROUP, String.format(TAX_DEADLINE_CACHE_KEY, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/utils/TaxDeadLineUtil.java:49:        String deadLine = (String) redisCacheClient.get(REDIS_GROUP, String.format(DEAD_LINE_KEY, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/utils/TaxDeadLineUtil.java:57:        return (String) redisCacheClient.get(REDIS_GROUP, String.format(DEAD_LINE_KEY, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/utils/TaxDeadLineUtil.java:61:        redisCacheClient.remove(REDIS_GROUP, String.format(DEAD_LINE_KEY, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/state/abs/AbsDeclarationManageTemplateService.java:308:            if (!redisCacheClient.isExist(REDIS_GROUP, freshKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/state/abs/AbsDeclarationManageTemplateService.java:313:                redisCacheClient.put(REDIS_GROUP, freshKey, "ok", DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/state/abs/AbsDeclarationManageTemplateService.java:320:            if (!redisCacheClient.isExist(REDIS_GROUP, freshKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/state/abs/AbsDeclarationManageTemplateService.java:321:                redisCacheClient.put(REDIS_GROUP, freshKey, "ok", DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/js/JsTaxFeedBackServiceImpl.java:298:            if (cacheClient.isExist(REDIS_GROUP, keyDown)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/js/JsTaxFeedBackServiceImpl.java:301:            cacheClient.put(REDIS_GROUP, keyDown, true, time);//设置过期时间为30秒
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/js/JsTaxFeedBackServiceImpl.java:312:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/js/JsTaxFeedBackServiceImpl.java:315:        cacheClient.put(REDIS_GROUP, key, true, DEFAULT_REDIS_LIVE_SECONDS);//设置过期时间为1天
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/sz/SzTaxFeedBackServiceImpl.java:224:        if (redisCacheClient.isExist(REDIS_GROUP, getMd5Key(customerId, period, MD5_FLAG))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/sz/SzTaxFeedBackServiceImpl.java:225:            if (StringUtils.equals(md5Hex, redisCacheClient.getString(REDIS_GROUP, getMd5Key(customerId, period, MD5_FLAG)))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/feedback/impl/sz/SzTaxFeedBackServiceImpl.java:262:        redisCacheClient.put(REDIS_GROUP, getMd5Key(customerId, period, MD5_FLAG), md5Hex, MD5_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/declarationSettings/impl/TaxApriProdDeductionSettingServiceImpl.java:955://        YhsSycjCshResultDTO yhsSycjCshResultDTO = (YhsSycjCshResultDTO) cacheClient.get(REDIS_GROUP, String.format("YHS_INIT_SETTING_%s_%s", customerId, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/declarationSettings/impl/TaxApriProdDeductionSettingServiceImpl.java:962://            cacheClient.put(REDIS_GROUP, String.format("YHS_INIT_SETTING_%s_%s", customerId, period), yhsSycjCshResultDTO, INIT_RES_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/ShTaxSingleSignOnServiceImpl.java:136:                if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/ShTaxSingleSignOnServiceImpl.java:137:                    return (ShSsoResponse) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/ShTaxSingleSignOnServiceImpl.java:142:                cacheClient.put(REDIS_GROUP, key, shSsoResponse, Integer.valueOf(tgcTimeOut.getShTgcTimeout()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/NbTaxSingleSignOnServiceImpl.java:80://        if (cacheClient.isExist(REDIS_GROUP, key) && needCache) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/NbTaxSingleSignOnServiceImpl.java:81://            return (T) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/NbTaxSingleSignOnServiceImpl.java:95://        cacheClient.put(REDIS_GROUP, key, ssoResponse, this.getExpireTime());
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/GdTaxSingleSignOnServiceImpl.java:76://        if (cacheClient.isExist(REDIS_GROUP, key) && needCache) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/GdTaxSingleSignOnServiceImpl.java:77://            return (GdSsoResponse) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/GdTaxSingleSignOnServiceImpl.java:92://        cacheClient.put(REDIS_GROUP, key, gdSsoResponse, Integer.valueOf(tgcTimeOut.getGdTgcTimeout()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:170:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:194:            cacheClient.put(REDIS_GROUP, key, stateRegisterPwd, LOST_TIME_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:268:        if (cacheClient.isExist(REDIS_GROUP, key) && needCache) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:269:            return (GdSsoResponse) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:301:        cacheClient.put(REDIS_GROUP, key, gdSsoResponse, Integer.valueOf(tgcTimeOut.getHbTgcTimeout()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:352:        if (cacheClient.isExist(REDIS_GROUP, key) && needCache) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:353:            return (HbSsoResponse) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:410:        cacheClient.put(REDIS_GROUP, key, response, Integer.valueOf(tgcTimeOut.getHbTgcTimeout()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:500:        if (cacheClient.isExist(REDIS_GROUP, key) && needCache) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:501:            return (ShSsoResponse) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/sso/impl/TaxSingleSignOnServiceImpl.java:509:        cacheClient.put(REDIS_GROUP, key, shSsoResponse, Integer.valueOf(tgcTimeOut.getShTgcTimeout()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/workflow/impl/TaxSsContext.java:88:        //		if (cacheClient.isExist(REDIS_GROUP, String.format(TAX_SS_BIZ_BASE_DTO, customerId))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/workflow/impl/TaxSsContext.java:89:        //			baseDto = (TaxSsBizBaseDto) cacheClient.getDeclarationManagerService(REDIS_GROUP, String.format(TAX_SS_BIZ_BASE_DTO, customerId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/workflow/impl/TaxSsContext.java:109:        //			cacheClient.put(REDIS_GROUP, String.format(TAX_SS_BIZ_BASE_DTO, customerId), baseDto, timeout);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/guid/declarationurl/impl/IDeclarationUrlServiceImpl.java:58:        if (cacheClient.isExist(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/guid/declarationurl/impl/IDeclarationUrlServiceImpl.java:59:            return (Map<String, String>) cacheClient.get(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/guid/declarationurl/impl/IDeclarationUrlServiceImpl.java:69:        cacheClient.put(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode), urlMaps, LOST_TIME_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/guid/declarationurl/impl/IDeclarationUrlServiceImpl.java:76:        cacheClient.remove(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/guid/declarationurl/impl/IDeclarationUrlServiceImpl.java:90:        cacheClient.remove(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/guid/declarationurl/impl/IDeclarationUrlServiceImpl.java:139:                cacheClient.remove(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, param.getAreaCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/handler/TaxSynchQcxxHandler.java:236:            redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/handler/TaxSynchQcxxHandler.java:776:            String qcxxRequestStr = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/TaxPayInResultConsumer.java:90:                    cacheClient.remove(REDIS_GROUP, String.format(UNPAIDINFODETAILSBF_TAXNO, paymentResultDO.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/TaxPayInResultConsumer.java:91:                    cacheClient.remove(REDIS_GROUP, String.format(PAIDINFODETAILSBF_TAXNO, paymentResultDO.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/TaxPayInResultConsumer.java:93:                    cacheClient.remove(REDIS_GROUP, String.format(UNPAIDINFODETAIL_TAXNO, paymentResultDO.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/TaxPayInResultConsumer.java:94:                    cacheClient.remove(REDIS_GROUP, String.format(PAIDINFODETAIL_TAXNO, paymentResultDO.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/FdTaxPayInResultConsumer.java:90:                    cacheClient.remove(REDIS_GROUP, String.format(FD_UNPAIDINFODETAIL_TAXNO, paymentResultDO.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/FdTaxPayInResultConsumer.java:91:                    cacheClient.remove(REDIS_GROUP, String.format(FD_PAIDINFODETAIL_TAXNO, paymentResultDO.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/PreHdDownloadConsumer.java:58:        Map<Object, Object> mapAll = redisCacheClient.getMapAll(REDIS_GROUP, redisKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/PreHdDownloadConsumer.java:106:            redisCacheClient.remove(REDIS_GROUP, redisKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/mq/consumer/DeclarationStateChangedConsumer.java:98:            String result = redisCacheClient.set("first", digest.getBytes(), UUIDUtil.generateUuid().getBytes(), "NX".getBytes(), "PX".getBytes(), 10 * 1000);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/CrawlHelper.java:224:            if (!redisCacheClient.isExist(REDIS_GROUP, key)){
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/CrawlHelper.java:225:                redisCacheClient.put(REDIS_GROUP, key, true, 200);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/CrawlHelper.java:233:                redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/async/AsyncHandleFDHdCompareUtil.java:73:        if(redisCacheClient.isExist(REDIS_GROUP,redisKey)){
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/async/AsyncHandleFDHdCompareUtil.java:79:        if(redisCacheClient.isExist(REDIS_GROUP, lockKey)){
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/async/AsyncHandleFDHdCompareUtil.java:83:            redisCacheClient.put(REDIS_GROUP, lockKey, true, 120);//设置过期时间为2分钟
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/async/AsyncHandleFDHdCompareUtil.java:94:            redisCacheClient.put(REDIS_GROUP, redisKey, "ok", DateUtils.getNumberBetweenNowAndNextMonthMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/async/AsyncHandleFDHdCompareUtil.java:96:            redisCacheClient.remove(REDIS_GROUP, lockKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/DownLoadUtil.java:256:            redisCacheClient.putToMap(PreHdDownloadConsumer.REDIS_GROUP, redisKey, redisContext);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/basic/helper/DownLoadUtil.java:258:            redisCacheClient.expire(PreHdDownloadConsumer.REDIS_GROUP, redisKey, 60 * 60 * 24 * 10);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/wszm/impl/FdTaxWszmServiceImpl.java:116:        if (cacheClient.isExist(REDIS_GROUP, cacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/wszm/impl/FdTaxWszmServiceImpl.java:117:            String result = (String) cacheClient.get(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/wszm/impl/FdTaxWszmServiceImpl.java:148:            cacheClient.put(REDIS_GROUP, cacheKey, JSON.toJSONString(map), second);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/wszm/impl/FdTaxWszmServiceImpl.java:236:        cacheClient.remove(REDIS_GROUP, String.format(FD_WSZM_LIST_TAXNO, taxpayer.getNsrsbh()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ct/impl/CtCommonServiceImpl.java:287:                if (!cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ct/impl/TaxCtCorporateTaxSelectedBodyService.java:55:        if (!cacheClient.isExist(REDIS_GROUP, redisSeclectedGroupKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ct/impl/TaxCtCorporateTaxSelectedBodyService.java:61:                cacheClient.put(REDIS_GROUP, redisSeclectedGroupKey, selectedBodys, LOST_TIME_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ct/impl/TaxCtCorporateTaxSelectedBodyService.java:65:            selectedBodys = (List<TaxCtCorporateTaxSelectedBody>) cacheClient.get(
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ss/impl/guide/GuideReportServiceImpl.java:269:        if (cacheClient.isExist(REDIS_GROUP, cacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ss/impl/guide/GuideReportServiceImpl.java:281:        cacheClient.put(REDIS_GROUP, cacheKey, true, DateUtil.endOfMonth(new Date()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ss/impl/guide/GuideReportServiceImpl.java:299:        if (cacheClient.isExist(REDIS_GROUP, cacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ss/impl/guide/GuideReportServiceImpl.java:337:        cacheClient.put(REDIS_GROUP, cacheKey, true, DateUtil.endOfMonth(new Date()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigServiceImpl.java:186:        String reportConfig = (String) redisCacheClient.get(REDIS_GROUP, String.format(REPORT_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigServiceImpl.java:188:        redisCacheClient.remove(REDIS_GROUP, String.format(REPORT_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:64:            redisCacheClient.setex(REDIS_GROUP, phoneKey, 1800, phone);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:75:        String phone = redisCacheClient.getString(REDIS_GROUP, phoneKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:84:        redisCacheClient.setex(REDIS_GROUP, phoneSmsKey, 300, smsCode);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:93:        String phoneSmsCode = redisCacheClient.getString(REDIS_GROUP, phoneSmsKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:102:        redisCacheClient.setex(REDIS_GROUP, validKey, exprieTime, smsCode);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:103:        redisCacheClient.del(REDIS_GROUP, phoneSmsKey.getBytes());
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DeclarePreCheckServiceImpl.java:121:        return redisCacheClient.isExist(REDIS_GROUP, validKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/VatCompareServiceImpl.java:340:            redisCacheClient.remove(REDIS_GROUP, String.format("%s:%s:%s", DECLARE_DATA, customerId, taxCode));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:133:        declarationStateList.forEach(declarationState -> redisCacheClient.put(REDIS_GROUP, String.format(ETAX_TASK_SSD_KEY, taskId, declarationState.getTaxCode()), declarationState.getId(), ETAX_REPORT_GRAB_TTL));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:138:        taxTypeEnumMap.entrySet().stream().forEach(item -> redisCacheClient.put(REDIS_GROUP, String.format(ETAX_TASK_SSD_KEY, taskId, item.getKey().getCode()), item.getValue(), ETAX_REPORT_GRAB_TTL));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:143:        Object value = redisCacheClient.get(REDIS_GROUP, String.format(ETAX_TASK_SSD_KEY, taskId, taxCode));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:152:        redisCacheClient.remove(REDIS_GROUP, String.format(ETAX_TASK_SSD_KEY, taskId, taxCode));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:248:        redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:327://            redisCacheClient.put(REDIS_GROUP, String.format(ETAX_SUB_TASK_KEY, subTaskIdList.get(i)), JSON.toJSONString(taskMeta), ETAX_REPORT_GRAB_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:333:        redisCacheClient.put(REDIS_GROUP, String.format(ETAX_SUB_TASK_KEY, subTaskId), JSON.toJSONString(taskMeta), ETAX_REPORT_GRAB_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:338:        redisCacheClient.remove(REDIS_GROUP, String.format(ETAX_SUB_TASK_KEY, subTaskId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:351:        redisCacheClient.putToMap(REDIS_GROUP, key, grabMap);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:352:        redisCacheClient.expire(REDIS_GROUP, key, ETAX_REPORT_GRAB_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:364:            redisCacheClient.putToMap(REDIS_GROUP, reportGrabKey, etaxReportGrabResult.getSsdId(), JSON.toJSONString(etaxReportGrabResult));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:366:            grabMap = redisCacheClient.getMapAll(REDIS_GROUP, reportGrabKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:371:                redisCacheClient.putToMap(REDIS_GROUP, reportGrabKey, declarationState.getId(), JSON.toJSONString(etaxReportGrabResult));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:374:        grabMap = redisCacheClient.getMapAll(REDIS_GROUP, reportGrabKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:379:            // redisCacheClient.remove(REDIS_GROUP, reportGrabKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:391:        redisCacheClient.removeFromMap(REDIS_GROUP, reportGrabKey, ssdId);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:406:        String value = (String) redisCacheClient.get(REDIS_GROUP, String.format(ETAX_SUB_TASK_KEY, subTaskId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:423:        String value = String.valueOf(redisCacheClient.getFromMap(REDIS_GROUP, key, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/TaxAuxServiceImpl.java:424:        redisCacheClient.removeFromMap(REDIS_GROUP, key, ssdId);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownManager.java:110:                            redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownManager.java:138:                        redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownManager.java:195:                        redisCacheClient.remove(REDIS_GROUP, freshKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DropDownServiceImpl.java:53:            String jsonData = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/DropDownServiceImpl.java:58:                redisCacheClient.put(REDIS_GROUP, key, jsonData, EXPIRE);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:117:                redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:656:        if (redisCacheClient.isExist(REDIS_GROUP, freshKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:659:            redisCacheClient.put(REDIS_GROUP, freshKey, "ok", DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:667:        redisCacheClient.remove(REDIS_GROUP, freshKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:677:                redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:692:                redisCacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:702:            String jsonData = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:705:                redisCacheClient.put(REDIS_GROUP, key, jsonData);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:722:                String jsonData = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:725:                    redisCacheClient.put(REDIS_GROUP, key, jsonData);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:749:                String jsonData = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:752:                    redisCacheClient.put(REDIS_GROUP, key, jsonData);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:768:            String jsonData = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:785:            redisCacheClient.put(REDIS_GROUP, key, JSON.toJSONString(map), 60 * 60 * 24);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:794:            String filterValue = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:797:                redisCacheClient.put(REDIS_GROUP, key, filterValue);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:822:                String jsonData = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:826:                        redisCacheClient.put(REDIS_GROUP, key, jsonData, DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReduceDropDownServiceImpl.java:828:                        redisCacheClient.put(REDIS_GROUP, key, jsonData);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigReaderImpl.java:90:        if (cacheClient.isExist(REDIS_GROUP, String.format(REPORT_CONFIG_CACHE, taxTypeEnum.getCode()))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigReaderImpl.java:91:            return (ReportConfig) cacheClient.get(REDIS_GROUP, String.format(REPORT_CONFIG_CACHE, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigReaderImpl.java:109:            cacheClient.put(REDIS_GROUP, String.format(REPORT_CONFIG_CACHE, taxTypeEnum.getCode()), reportConfig);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigReaderImpl.java:187:        if (!StringUtils.equals("OK", redisCacheClient.set(REDIS_GROUP, lockKey.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 10))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigReaderImpl.java:197:                cacheClient.remove(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/impl/ReportConfigReaderImpl.java:200:            redisCacheClient.remove(REDIS_GROUP, lockKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:160:        redisCacheClient.put(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId), taxSendPlaceholder, expireTime);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:194:        Long incr = redisCacheClient.incr(REDIS_GROUP, String.format(DECLARE_RETRY_COUNT_KEY, taskId, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:195:        redisCacheClient.expire(REDIS_GROUP, String.format(DECLARE_RETRY_COUNT_KEY, taskId, ssdId), DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:200:        Long incr = redisCacheClient.incr(REDIS_GROUP, String.format(FEEDBACK_DECLARE_RETRY_COUNT_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:201:        redisCacheClient.expire(REDIS_GROUP, String.format(FEEDBACK_DECLARE_RETRY_COUNT_KEY, ssdId), DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:206:        redisCacheClient.putString(REDIS_GROUP, String.format(DECLARE_RETRY_COUNT_KEY, taskId, ssdId), "0");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:207:        redisCacheClient.expire(REDIS_GROUP, String.format(DECLARE_RETRY_COUNT_KEY, taskId, ssdId), DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:211:        redisCacheClient.putString(REDIS_GROUP, String.format(FEEDBACK_DECLARE_RETRY_COUNT_KEY, ssdId), "0");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:212:        redisCacheClient.expire(REDIS_GROUP, String.format(FEEDBACK_DECLARE_RETRY_COUNT_KEY, ssdId), DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:216:        redisCacheClient.remove(REDIS_GROUP, String.format(DECLARE_RETRY_COUNT_KEY, taskId, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:221:        redisCacheClient.put(REDIS_GROUP, String.format(SSD_RETRY_KEY, ssdId), Boolean.TRUE, DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:225:        redisCacheClient.put(REDIS_GROUP, String.format(INSTANT_TO_TIMED_KEY, ssdId), Boolean.TRUE, DECLARE_PLACEHOLDER_TTL_LONG);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:229:        Boolean result = (Boolean) redisCacheClient.get(REDIS_GROUP, String.format(INSTANT_TO_TIMED_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:237:        redisCacheClient.putString(REDIS_GROUP, String.format(SSD_TASKID_KEY, ssdId), taskId);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:238:        redisCacheClient.expire(REDIS_GROUP, String.format(SSD_TASKID_KEY, taskId, ssdId), DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:242:        redisCacheClient.putString(REDIS_GROUP, String.format(FCK_RETRYING, ssdId), "Y");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:243:        redisCacheClient.expire(REDIS_GROUP, String.format(FCK_RETRYING, ssdId), 10 * 60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:247:        redisCacheClient.putString(REDIS_GROUP, String.format(DECLARING_FLAG, ssdId), "Y");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:248:        redisCacheClient.expire(REDIS_GROUP, String.format(DECLARING_FLAG, ssdId), 5 * 60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:252:        return redisCacheClient.getString(REDIS_GROUP, String.format(DECLARING_FLAG, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:256:        redisCacheClient.remove(REDIS_GROUP, String.format(DECLARING_FLAG, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:260:        return redisCacheClient.getString(REDIS_GROUP, String.format(FCK_RETRYING, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:264:        redisCacheClient.remove(REDIS_GROUP, String.format(FCK_RETRYING, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:269:        return (Boolean) redisCacheClient.get(REDIS_GROUP, String.format(SSD_RETRY_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:273:        return redisCacheClient.getString(REDIS_GROUP, String.format(SSD_TASKID_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:277:        redisCacheClient.remove(REDIS_GROUP, String.format(SSD_TASKID_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:281:        redisCacheClient.remove(REDIS_GROUP, String.format(INSTANT_TO_TIMED_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:285:        redisCacheClient.remove(REDIS_GROUP, String.format(SSD_RETRY_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:290:        String value = redisCacheClient.getString(REDIS_GROUP, String.format(DECLARE_RETRY_COUNT_KEY, taskId, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:298:        String value = redisCacheClient.getString(REDIS_GROUP, String.format(FEEDBACK_DECLARE_RETRY_COUNT_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:306:        redisCacheClient.remove(REDIS_GROUP, String.format(FEEDBACK_DECLARE_RETRY_COUNT_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:310:        redisCacheClient.remove(REDIS_GROUP, String.format(SSD_OTHER_MAP_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:315:            redisCacheClient.putString(REDIS_GROUP, String.format(SSD_OTHER_MAP_KEY, ssdId), JSON.toJSONString(otherMap));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:316:            redisCacheClient.expire(REDIS_GROUP, String.format(SSD_OTHER_MAP_KEY, ssdId), 1800);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:321:        String value = redisCacheClient.getString(REDIS_GROUP, String.format(SSD_OTHER_MAP_KEY, ssdId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:329:        redisCacheClient.put(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId), taxSendPlaceholder, DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:336:        return (TaxSendPlaceholder) redisCacheClient.get(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:343:        TaxSendPlaceholder taxSendPlaceholder = (TaxSendPlaceholder) redisCacheClient.get(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:351:        redisCacheClient.putString(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRMED, taskId), "true");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:352:        redisCacheClient.expire(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRMED, taskId), 600);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:356:        String value = redisCacheClient.getString(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRMED, taskId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:380:        redisCacheClient.put(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId), taxSendPlaceholder, DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:431:        redisCacheClient.put(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId), taxSendPlaceholder, DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/DeclareHelper.java:540:        redisCacheClient.put(REDIS_GROUP, String.format(DECLARE_HOLDER_KEY, taskId), taxSendPlaceholder, DECLARE_PLACEHOLDER_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/utils/CtSendCheckUtil.java:974:                if (!cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/common/TaxCalculateExecutorImpl.java:165:            Object o = redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:120:        boolean needLock = redisCacheClient.isExist(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:203:        map.put("lockState",redisCacheClient.isExist(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode())));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:295:            boolean exist = redisCacheClient.isExist(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:336:        Object lock = redisCacheClient.get(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:341:        validatorRuleContent = (String) redisCacheClient.get(REDIS_GROUP, String.format(VAL_RULE_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:342:        redisCacheClient.remove(REDIS_GROUP, String.format(VAL_RULE_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:391:        String lockTime = (String)redisCacheClient.get(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:392:        redisCacheClient.remove(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/ValidatorManagerServiceImpl.java:423:        redisCacheClient.setNx(REDIS_GROUP, String.format(VAL_RULE_LOCK_KEY, taxTypeEnum.getCode()),currentTime);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/TaxConfigSyncImpl.java:181:        redisCacheClient.put(REDIS_GROUP, wrapBizUniqueKey(syncComponent, param), decrypt, SYNC_CONFIG_CACHE_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/TaxConfigSyncImpl.java:275:        String cacheCfg = (String) redisCacheClient.get(REDIS_GROUP, uniqueKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/TaxConfigSyncImpl.java:276:        redisCacheClient.remove(REDIS_GROUP, uniqueKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/MenuManageServiceImpl.java:343:        if (redisCacheClient.isExist(REDIS_GROUP, MENU_LIST)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/MenuManageServiceImpl.java:344:            String menu = (String) redisCacheClient.get(REDIS_GROUP, MENU_LIST);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/MenuManageServiceImpl.java:349:            redisCacheClient.put(REDIS_GROUP, MENU_LIST, JSON.toJSONString(categoryList), 60 * 60 * 24);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/MenuManageServiceImpl.java:479:        String menusContent = (String) redisCacheClient.get(REDIS_MENU_GROUP, MENU_KEY);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/MenuManageServiceImpl.java:485:        redisCacheClient.remove(REDIS_MENU_GROUP, MENU_KEY);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:132:            boolean needLock = redisCacheClient.isExist(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:275:        map.put("lockState",redisCacheClient.isExist(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode())));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:371:            boolean exist = redisCacheClient.isExist(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:645:        Object lock = redisCacheClient.get(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:650:        calculatorRuleContent = (String) redisCacheClient.get(REDIS_GROUP, String.format(CAL_RULE_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:651:        redisCacheClient.remove(REDIS_GROUP, String.format(CAL_RULE_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:741:        redisCacheClient.setNx(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode()),currentTime);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:761:        String lockTime = (String)redisCacheClient.get(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/manage/impl/CalculatorManagerServiceImpl.java:762:        redisCacheClient.remove(REDIS_GROUP, String.format(CAL_RULE_LOCK_KEY, taxTypeEnum.getCode()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/impl/TxOutputAddedValueServiceImpl.java:458:            o = redisCacheClient.get(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/impl/TxOutputAddedValueServiceImpl.java:471:            redisCacheClient.remove(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/impl/TxOutputAddedValueServiceImpl.java:1841:            redisCacheClient.put(REDIS_GROUP, String.format("%s:%s","selectSetting", customerId),"Y", 10 * 60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/impl/TxOutputAddedValueServiceImpl.java:1872:            Object object = redisCacheClient.get(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRM_REDIS_KEY, declarationState.getCustomerId(), declarationState.getPeriod()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/impl/TxOutputAddedValueServiceImpl.java:1878://            redisCacheClient.remove(REDIS_GROUP, String.format(INPUT_INVOICE_CONFIRM_REDIS_KEY, declarationState.getCustomerId(), declarationState.getPeriod()));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/integrationtable/impl/TaxIntegrationTableServiceImpl.java:126:        ZzsybsbSbbdxx queryData = (ZzsybsbSbbdxx) cacheClient.get(REDIS_GROUP, redisDictionGroupKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/integrationtable/impl/TaxIntegrationTableServiceImpl.java:158:            cacheClient.put(REDIS_GROUP, redisDictionGroupKey, zzsybsbSbbdxxDto, 30);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/ot/helper/OtCommonReportCustomConfigService.java:361:            Object systemInit = redisCacheClient.get(REDIS_GROUP, String.format("%s:%s", "selectSetting", customerId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:150:        if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:151:            baseCodeMap = (Map<String, String>) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:162:        redisCacheClient.put(REDIS_GROUP, key, baseCodeMap, 60 * 60 * 24 * 30);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:206:            if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:207:                hdJmxxes = (List<HdJmxx>) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:219:            redisCacheClient.put(REDIS_GROUP, key, hdJmxxes, 60 * 60 * 24);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:223:        if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:224:            hdJmxxes = (List<HdJmxx>) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:246:        redisCacheClient.put(REDIS_GROUP, key, hdJmxxes, 60 * 60 * 24);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:254:        return StringUtils.equals("OK", redisCacheClient.set(REDIS_GROUP, key.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), intervalTime));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/cxs/utils/CxsSourceUtil.java:259:        return redisCacheClient.isExist(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/rp/helper/ResidualPremiumHelper.java:109:        if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/rp/helper/ResidualPremiumHelper.java:110:            String jsxsStr = (String) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/single/rp/helper/ResidualPremiumHelper.java:146:        redisCacheClient.put(REDIS_GROUP, key, jsxs, 60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/impl/FdKeyDataCompareServiceImpl.java:186:        if (redisCacheClient.isExist(REDIS_GROUP, getLogPath(customerId, ssdId, taskId))) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/impl/FdKeyDataCompareServiceImpl.java:277:            redisCacheClient.put(REDIS_GROUP, getLogPath(fdKeyDataCompareResult.getCustomerId(), fdKeyDataCompareResult.getSsdId(), taskId), 1, 600);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/async/AbstractDownloadUtil.java:363:        redisCacheClient.remove(REDIS_GROUP, String.format(CTA_DOWNLOAD_RPAHD, taxNo, period));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/async/AbstractDownloadUtil.java:367:        return "OK".equals(redisCacheClient.set(REDIS_GROUP, String.format(CTA_DOWNLOAD_RPAHD, taxNo, period).getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 40));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/YhsFdKeyDataCompareTaxService.java:275:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:50:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:63:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:78:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:91:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:106:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:119:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:134:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:147:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:162:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:175:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:190:                redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/declare/common/datacompare/tax/HsqjSyncDownLoadUtil.java:203:            redisCacheClient.put(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId), "2", 60 * 5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fts/impl/FtsServiceImpl.java:190:        if (redisCacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fts/impl/FtsServiceImpl.java:191:            List<QueryJmxxDetail> queryJmxxDetailList = (List<QueryJmxxDetail>) redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/fts/impl/FtsServiceImpl.java:214:        redisCacheClient.put(REDIS_GROUP, key, queryJmxxDetailList, DateUtils.getNumberBetweenNowAndTomorrowMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/common/service/impl/DeviceRegisterServiceImpl.java:29:        redisCacheClient.sadd(REDIS_GROUP, String.format(DEVICE_ID_KEY, customerId), deviceId);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/common/service/impl/DeviceRegisterServiceImpl.java:30:        redisCacheClient.expire(REDIS_GROUP, String.format(DEVICE_ID_KEY, customerId), DEVICE_ID_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/common/service/impl/DeviceRegisterServiceImpl.java:35:        redisCacheClient.srem(REDIS_GROUP, String.format(DEVICE_ID_KEY, customerId), deviceId);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/common/service/impl/DeviceRegisterServiceImpl.java:41:        Set<String> smembers = redisCacheClient.smembers(REDIS_GROUP, String.format(DEVICE_ID_KEY, customerId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/common/service/impl/DeviceRegisterServiceImpl.java:50:        redisCacheClient.remove(REDIS_GROUP, String.format(DEVICE_ID_KEY, customerId));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/common/service/impl/WhiteListServiceImpl.java:340:        Object o = redisCacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/deferLevy/impl/OverPaidDrawBackServiceImpl.java:159:            cacheClient.put(REDIS_GROUP, key, true, 86400 * 31);//保存一个月
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/deferLevy/impl/OverPaidDrawBackServiceImpl.java:172:            if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/deferLevy/impl/DeferLevyServiceImpl.java:99:            redisCacheClient.put(REDIS_GROUP, cacheKey, JSONArray.toJSONString(list), DateUtils.getNumberBetweenNowAndNextMonthMorning(ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/riskCheck/impl/RiskCheckServiceImpl.java:50:            cacheClient.put(REDIS_GROUP, key, true, DEFAULT_CACHE_TTL);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:472:        Object object= redisCacheClient.get(REDIS_GROUP, "fd:ldts:reasons");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:493:                redisCacheClient.put(REDIS_GROUP, "fd:ldts:reasons", JSON.toJSONString(notApplyReasons), 30 * 24 * 60 * 60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:501:        Object object= redisCacheClient.get(REDIS_GROUP, "fd:lddq:reasons");
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:520:                redisCacheClient.put(REDIS_GROUP, "fd:lddq:reasons", JSON.toJSONString(notApplyReasons), 30 * 24 * 60 * 60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:743:        if (redisCacheClient.isExist(REDIS_GROUP, keyDown)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:763:            Set<String> keys = redisCacheClient.keySet(keyOld);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:764:            redisCacheClient.remove(REDIS_GROUP, keys.toArray(new String[keys.size()]));*/
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/ldts/LdtsImpl.java:765:            redisCacheClient.put(REDIS_GROUP, keyDown, "ok", DateUtils.getNumberBetweenNowAndUnknownMorning(7, ChronoUnit.SECONDS));
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:224:        cacheClient.put(REDIS_GROUP, String.format(SFXY_TAXNO, taxNo), JSON.toJSONString(tripartiteAgreementDetailList), ttlSeconds);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:352:                cacheClient.remove(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:440:        cacheClient.remove(REDIS_GROUP,key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:485:            if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:487:                String result = (String) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:511:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:521:            cacheClient.remove(REDIS_GROUP,"UNPAIDINFODETAILSBF_TAXNO_"+taxNo);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:522:            cacheClient.remove(REDIS_GROUP,"PAIDINFODETAILSBF_TAXNO_"+taxNo);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:524:            cacheClient.remove(REDIS_GROUP,"UNPAIDINFODETAIL_TAXNO"+taxNo);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:525:            cacheClient.remove(REDIS_GROUP,"PAIDINFODETAIL_TAXNO"+taxNo);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:599:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:600:            String result = (String)cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:615:            if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:616:                String result = (String)cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:642:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:643:            String result = (String)cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:704:        cacheClient.put(REDIS_GROUP, key, JSON.toJSONString(map), StringUtils.isEmpty(expireTime) ? 60*2 : Integer.parseInt(expireTime)*60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:705:        //cacheClient.put(REDIS_GROUP, key, map, 120);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:710:        String result = (String) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:740:        cacheClient.remove(REDIS_GROUP,key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:741:        cacheClient.put(REDIS_GROUP, key, JSON.toJSONString(map), StringUtils.isEmpty(expireTime) ? 60*5 : Integer.parseInt(expireTime)*60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:747:        cacheClient.put(REDIS_GROUP, String.format(SFXY_TAXNO, taxNo), JSON.toJSONString(target), 60*5);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:807:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:808:            return (String) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/payment/impl/HenanOnlinePaymentServiceImpl.java:818:                cacheClient.put(REDIS_GROUP,yzpzxhCzlxdmMap.getKey(),result.getTaskId(),StringUtils.isEmpty(expireTime) ? 60*10 : Integer.parseInt(expireTime)*60);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:335:        if (!update && cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:336:            String resultValue = (String) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:344:        cacheClient.put(REDIS_GROUP, key, JSON.toJSONString(result), ttlSeconds);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:407:        if (cacheClient.isExist(REDIS_GROUP, cacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:408:            String result = (String) cacheClient.get(REDIS_GROUP, cacheKey);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:435:            cacheClient.put(REDIS_GROUP, cacheKey, JSON.toJSONString(map), second);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:496:        if (cacheClient.isExist(REDIS_GROUP, cacheKey)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:497:            FdUnpaidInfoDTO fdUnpaidInfoDTO = JSON.parseObject((String) cacheClient.get(REDIS_GROUP, cacheKey), FdUnpaidInfoDTO.class);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/online/fd/payment/impl/FdOnlinePaymentServiceImpl.java:507:            cacheClient.put(REDIS_GROUP, cacheKey, JSON.toJSONString(fdUnpaidInfoDTO), second);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/taxCollection/impl/TaxCollectionDictionaryServiceImpl.java:30:        if (cacheClient.isExist(REDIS_GROUP, key)) {
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/taxCollection/impl/TaxCollectionDictionaryServiceImpl.java:31:            return (TaxCollectionDictionaryItem) cacheClient.get(REDIS_GROUP, key);
./taxcore/xqy-taxcore-core/src/main/java/cn/com/servyou/xqy/taxcore/core/taxCollection/impl/TaxCollectionDictionaryServiceImpl.java:36:        cacheClient.put(REDIS_GROUP, key, taxCollectionDictionaryItem);
