./xtax/xqy-tax-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/nt/ot/TaxDeclareController.java:269:        redisCacheClient.put(REDIS_GROUP, String.format("%s:%s:%s", DECLARE_DATA, declareRequestJOs.getCustomerId(), declareRequestJOs.getTaxCode()), JSON.toJSONString(declareRequestJOs), 30 * 24 * 60 * 60);
./xtax/xqy-tax-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/facade/nt/common/TaxFacadeSsHdxxController.java:116:            if (cacheClient.isExist(REDIS_GROUP, key)) {
./xtax/xqy-tax-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/facade/nt/common/TaxFacadeSsHdxxController.java:119:                cacheClient.put(REDIS_GROUP, key, true, 60);//设置过期时间为1分钟
./xtax/xqy-tax-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/st/TaxSsoTaxBureauController.java:103://                if (cacheClient.isExist("first", String.format(TaxRedisKeyConstants.TAX_SSO_KEY, areaCode, customerId))) {
./xtax/xqy-tax-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/st/TaxSsoTaxBureauController.java:105://                    taxSsoResultJO.setTtl(cacheClient.ttl("first", String.format(TaxRedisKeyConstants.TAX_SSO_KEY, areaCode, customerId)));
./xtax/xqy-tax-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/st/TaxSsoTaxBureauController.java:244://        cacheClient.put("first", String.format(TaxRedisKeyConstants.TAX_SSO_KEY, areaCode, customerId), content, TaxRedisKeyConstants.TAX_SSO_TTL);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:66:        if (!cacheClient.isExist(redisGroup, redisDictionGroupKey)) {
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:74:            cacheClient.put(redisGroup, redisDictionGroupKey, code, BASIC_DICTIONARY_TTL);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:76:            code = (String) cacheClient.get(redisGroup, redisDictionGroupKey);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:88:        if (!cacheClient.isExist(redisGroup, redisDictionGroupKey)) {
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:91:                cacheClient.put(redisGroup, redisDictionGroupKey, dictionaryList, BASIC_DICTIONARY_TTL);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:94:            dictionaryList = (List<DictionaryBasicDto>) cacheClient.get(redisGroup, redisDictionGroupKey);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:128:        Map<String, String> dictionaryMap = (Map<String, String>) cacheClient.get(redisGroup, redisDictionGroupKey);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:136:            cacheClient.put(redisGroup, redisDictionGroupKey, dictionaryMap, BASIC_DICTIONARY_TTL);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:156:        Map<String, String> dictionaryMap = (Map<String, String>) cacheClient.get(redisGroup, redisDictionGroupKey);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:164:            cacheClient.put(redisGroup, redisDictionGroupKey, dictionaryMap, BASIC_DICTIONARY_TTL);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:184:        List<Dictionary> dicCashList = (List<Dictionary>) cacheClient.get(redisGroup, redisKey);
./xtax/xqy-tax-basic/src/main/java/cn/com/servyou/xqy/tax/basic/service/DictionaryBasicService.java:188:            cacheClient.put(redisGroup, redisKey, dictionaryList, BASIC_DICTIONARY_TTL);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/ct/impl/TaxCtCorporateTaxSelectedBodyService.java:52:        if (!cacheClient.isExist(REDIS_GROUP, redisSeclectedGroupKey)) {
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/ct/impl/TaxCtCorporateTaxSelectedBodyService.java:57:                cacheClient.put(REDIS_GROUP, redisSeclectedGroupKey, selectedBodys, LOST_TIME_TTL);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/ct/impl/TaxCtCorporateTaxSelectedBodyService.java:61:            selectedBodys = (List<TaxCtCorporateTaxSelectedBodyDO>) cacheClient.get(REDIS_GROUP, redisSeclectedGroupKey);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/agent/cancel/gd/SSTaxCancelService.java:63://        if (!cacheClient.isExist(REDIS_GROUP, redisSeclectedGroupKey)) {
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/agent/cancel/gd/SSTaxCancelService.java:66://                cacheClient.put(REDIS_GROUP, redisSeclectedGroupKey, tgc, LOST_TIME);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/agent/cancel/gd/SSTaxCancelService.java:69://            tgc = (String) cacheClient.get(REDIS_GROUP, redisSeclectedGroupKey);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/common/impl/HdParamSettingServiceImpl.java:70:        if (cacheClient.isExist(REDIS_GROUP, CACHE_KEY)) {
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/common/impl/HdParamSettingServiceImpl.java:74:            cacheClient.put(REDIS_GROUP, CACHE_KEY, StringUtils.EMPTY, 7200);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/mobile/TaxMobileOverviewServiceImpl.java:634:                if (cacheClient.isExist(redisGroup, getInvoiceKey(customerIdLong, taxType, period, areaCode))) {
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/mobile/TaxMobileOverviewServiceImpl.java:652:                        cacheClient.remove(redisGroup, getInvoiceKey(customerIdLong, taxType, period, areaCode));
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/mobile/TaxMobileOverviewServiceImpl.java:741:        cacheClient.put(redisGroup, getInvoiceKey(customerId, taxType, period, areaCode), "", getExpireDate());
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/report/impl/TaxReportItemService.java:51:        List<TaxReportItem> taxReportItemList = (List<TaxReportItem>) cacheClient.get(REDIS_GROUP, redisDictionGroupKey);
./xtax/xqy-tax-core/src/main/java/cn/com/servyou/xqy/tax/core/report/impl/TaxReportItemService.java:55:                cacheClient.put(REDIS_GROUP, redisDictionGroupKey, taxReportItemList, TAX_REDIS_REPORT_ITEM_TTL);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/common/TaxDeclarationStateController.java:553:                    if (cacheClient.isExist(redisGroup, key)) {
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/common/TaxDeclarationStateController.java:555:                        cacheClient.remove(redisGroup, key);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/common/TaxDeclarationStateController.java:1222:                    if (!"OK".equals(redisCacheClient.set(redisGroup, cacheKey.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), declareSyncInterval))) {
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/common/TaxDeclarationStateController.java:1264:            redisCacheClient.remove(redisGroup, cacheKey);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/compare/CompareController.java:212:        Long count = redisCacheClient.incr(redisGroup, countKey);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/compare/CompareController.java:213:        redisCacheClient.nativeExpire(redisGroup, countKey, retryService.getRetryCacheTtl());
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/compare/FdKeyDataCompareController.java:79:                    String cacheVal = (String)redisCacheClient.get(REDIS_GROUP, String.format(REDIS_TAX_DATA,taskId));
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/ot/TaxOtReportController.java:624:                    if (!"OK".equals(redisCacheClient.set(redisGroup, cacheKey.getBytes(), "lock".getBytes(), "NX".getBytes(), "EX".getBytes(), 10000))) {
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/declare/ot/TaxOtReportController.java:643:            redisCacheClient.remove(redisGroup, cacheKey);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/receive/BasicReceiveController.java:52:            redisCacheClient.put(REDIS_GROUP, MENU_KEY, decrypt, DATA_TTL);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/receive/BasicReceiveController.java:55:            redisCacheClient.put(REDIS_GROUP, MENU_LINK_KEY, decrypt, DATA_TTL);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/common/rule/RuleController.java:60:            redisCacheClient.put(REDIS_GROUP, String.format(CAL_RULE_KEY, ruleJO.getTaxCode()), decrypt, RULE_TTL);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/common/rule/RuleController.java:62:            redisCacheClient.put(REDIS_GROUP, String.format(VAL_RULE_KEY, ruleJO.getTaxCode()), decrypt, RULE_TTL);
./xtax/xqy-tax-smart-web/src/main/java/cn/com/servyou/xqy/portal/web/tax/common/rule/RuleController.java:65:            redisCacheClient.put(REDIS_GROUP, String.format(REPORT_KEY, ruleJO.getTaxCode()), decrypt, RULE_TTL);
./xtax/xqy-tax-facadeimpl/src/main/java/cn/com/servyou/xqy/tax/facadeimpl/invoice/TaxPayerVatCodeFacadeImpl.java:115:                    if (cacheClient.isExist(REDIS_GROUP, lockKey)) {
./xtax/xqy-tax-integration/src/main/java/cn/com/servyou/xqy/tax/integration/taxcore/impl/TaxFacadeClientImpl.java:112:            redisCacheClient.put(REDIS_GROUP, String.format("%s:%s", "selectSetting", customerId), "Y", 10 * 60);
./xtax/xqy-tax-integration/src/main/java/cn/com/servyou/xqy/tax/integration/gateway/hb/impl/HbGatewayHttpClientImpl.java:64:            if (cacheClient.isExist(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId))) {
./xtax/xqy-tax-integration/src/main/java/cn/com/servyou/xqy/tax/integration/gateway/hb/impl/HbGatewayHttpClientImpl.java:65:                businessRequest.setTgc((String) cacheClient.get(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId)));
./xtax/xqy-tax-integration/src/main/java/cn/com/servyou/xqy/tax/integration/gateway/hb/impl/HbGatewayHttpClientImpl.java:70:                cacheClient.put(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId), tgc, Integer.valueOf(etaxConfig.getTgcTimeout()));
./xtax/xqy-tax-integration/src/main/java/cn/com/servyou/xqy/tax/integration/gateway/hb/impl/HbGatewayHttpClientImpl.java:77:                    cacheClient.remove(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId));
./xtax/xqy-tax-integration/src/main/java/cn/com/servyou/xqy/tax/integration/gateway/hb/impl/HbGatewayHttpClientImpl.java:84:                cacheClient.remove(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/ct/service/TaxCtCorporateTaxSelectedBodyService.java:54:        if (!cacheClient.isExist(REDIS_GROUP, redisSeclectedGroupKey)) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/ct/service/TaxCtCorporateTaxSelectedBodyService.java:59:                cacheClient.put(REDIS_GROUP, redisSeclectedGroupKey, selectedBodys, LOST_TIME_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/ct/service/TaxCtCorporateTaxSelectedBodyService.java:63:            selectedBodys = (List<TaxCtCorporateTaxSelectedBody>) cacheClient.get(
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/workflow/TaxSsContext.java:82:        //		if (cacheClient.isExist(REDIS_GROUP, String.format(TAX_SS_BIZ_BASE_DTO, customerId))) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/workflow/TaxSsContext.java:83:        //			baseDto = (TaxSsBizBaseDto) cacheClient.get(REDIS_GROUP, String.format(TAX_SS_BIZ_BASE_DTO, customerId));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/workflow/TaxSsContext.java:103:        //			cacheClient.put(REDIS_GROUP, String.format(TAX_SS_BIZ_BASE_DTO, customerId), baseDto, timeout);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/service/hb/HbTaxSingleSignOnService.java:226:        if (cacheClient.isExist(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId))) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/service/hb/HbTaxSingleSignOnService.java:227:            return (String) cacheClient.get(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/service/hb/HbTaxSingleSignOnService.java:261:        cacheClient.put(REDIS_GROUP, String.format(CacheKeyConstant.HB_TGC_KEY, customerId), tgc, Integer.valueOf(etaxConfig.getTgcTimeout()));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/service/sn/SnOtherTaxEditWayService.java:85://        SnTaxFxRedirectDTO snTaxFxRedirectDTO = (SnTaxFxRedirectDTO) cacheClient.get(TaxRedisKeyConstants.REDIS_GROUP, redisKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/nt/common/service/sn/SnOtherTaxEditWayService.java:96://            cacheClient.put(TaxRedisKeyConstants.REDIS_GROUP, redisKey, snTaxFxRedirectDTO, TAXSTATE_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/state/DeclarationStateRepairServiceImpl.java:199:        String result = redisCacheClient.set("first", "REPAIR_TASK".getBytes(), UUIDUtil.generateUuid().getBytes(), "NX".getBytes(), "PX".getBytes(), 3 * 60 * 1000);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/auto/service/AutoTgcService.java:90://        if (!cacheClient.isExist(redisGroup, rsaPublicKey)) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/auto/service/AutoTgcService.java:102://            cacheClient.put(redisGroup, rsaPublicKey, pk, LOST_TIME_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/auto/service/AutoTgcService.java:105://            return (String) cacheClient.get(redisGroup, rsaPublicKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/JsDzswjSSOService.java:212:        cacheClient.remove(redisGroup, key);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TaxForwardMessageService.java:120:        long count = cacheClient.incr(redisGroup, String.format(TaxRedisKeyConstants.TAX_MANAGE_FINISH_KEY, period, String.valueOf(txTypeConfirms.get(0).getCustomerId())));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TaxForwardMessageService.java:123:            cacheClient.nativeExpire(redisGroup, String.format(TaxRedisKeyConstants.TAX_MANAGE_FINISH_KEY, period, String.valueOf(txTypeConfirms.get(0).getCustomerId())),60*60*24*90);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/CreditService.java:982:    //        if (!cacheClient.isExist(redisGroup, asyncNumberCacheKey)) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/CreditService.java:996:    //            cacheClient.put(redisGroup, asyncNumberCacheKey, asyncNumber, BASIC_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/CreditService.java:999:    //            asyncNumber = (String) cacheClient.get(redisGroup, asyncNumberCacheKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/CreditService.java:1016:    //                cacheClient.remove(redisGroup, asyncNumberCacheKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TaxTypeDeclarationStateService.java:141:        cacheClient.put(REDIS_GROUP, key, taxStateMap, TAXSTATE_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TaxTypeDeclarationStateService.java:228:        return (Map<Long, Object>) cacheClient.get(REDIS_GROUP, redisKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TxNeedDeclarationSettingService.java:222:            if (!cacheClient.isExist(REDIS_GROUP, redisSeclectedGroupKey)) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TxNeedDeclarationSettingService.java:227:                cacheClient.put(REDIS_GROUP, redisSeclectedGroupKey, resultList, LOST_TIME_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/TxNeedDeclarationSettingService.java:231:                resultList = (List<TxNeedDeclarationSettingDto>) cacheClient.get(REDIS_GROUP, redisSeclectedGroupKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/AbstractTaxProgressMessageService.java:86:            String idListStr = (String) redisCacheClient.getFromMap(REDIS_GROUP, redisKey, criteria.getAccountId());
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/AbstractTaxProgressMessageService.java:104:            redisCacheClient.putToMap(REDIS_GROUP, redisKey, criteria.getAccountId(), JSON.toJSONString(customerIdList));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/AbstractTaxProgressMessageService.java:105:            redisCacheClient.expire(REDIS_GROUP, redisKey, EXPIRE_TIME);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/AbstractTaxProgressMessageService.java:125:        String haveDone = redisCacheClient.set(REDIS_GROUP, getHaveDoneKey(taxProgressMessageDTO).getBytes(), HAVE_DONE_VALUE.getBytes(), REDIS_NX.getBytes(), REDIS_EX.getBytes(), REDIS_HAVE_DONE_TIMEOUT);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/DeclarationUrlService.java:46:        if (cacheClient.isExist(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode))) {
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/DeclarationUrlService.java:47:            return (Map<String, String>) cacheClient.get(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/DeclarationUrlService.java:56:        cacheClient.put(REDIS_GROUP, String.format(DZSWJ_DECLARATION_URL, areaCode), urlMaps, LOST_TIME_TTL);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/service/HdxxHandleService.java:45:        return (HDXXCacheDto) cacheClient.get("first", key);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/st/datagram/common/hdxx/resolver/CacheHDXXResolver.java:30:        cacheClient.put("first", key, input.getCacheDto(), 60 * 60 * 24 * 30);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/common/GdQcxxHjNotifyConsumer.java:38:            redisCacheClient.put(REDIS_GROUP, getResultCacheKey(mqMessageDTO.getTaskId()), sfxshjzc, 60);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/common/GdQcxxHjNotifyConsumer.java:39:            redisCacheClient.remove(REDIS_GROUP, getTaskCacheKey(mqMessageDTO.getTaskId()));
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/mobile/TaxDeclarationStateMobileServiceImpl.java:65:        Object cache = cacheClient.get(REDIS_GROUP, redisFreshKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/mobile/TaxDeclarationStateMobileServiceImpl.java:72:            cacheClient.put(REDIS_GROUP, redisFreshKey, lastFreshTime);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/mobile/TaxDeclarationStateMobileServiceImpl.java:331:        Object cache = cacheClient.get(REDIS_GROUP, redisFreshKey);
./xtax/xqy-tax-service/src/main/java/cn/com/servyou/xqy/tax/mobile/TaxDeclarationStateMobileServiceImpl.java:339:            cacheClient.put(REDIS_GROUP, redisFreshKey, lastFreshTime);
