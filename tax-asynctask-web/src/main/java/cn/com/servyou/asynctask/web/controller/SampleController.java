package cn.com.servyou.asynctask.web.controller;

import cn.com.servyou.asynctask.core.service.RedisService;
import cn.com.servyou.asynctask.core.service.TestService;
import cn.com.servyou.asynctask.dataobj.BizTaskLogDO;
import cn.com.servyou.asynctask.shared.TestManagement;
import cn.com.servyou.xqy.framework.exception.BusinessException;
import cn.com.servyou.xqy.framework.exception.ExceptionDefinition;
import cn.com.servyou.xqy.framework.web.rest.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
* Controller示例
*/
@Controller
@RequestMapping
public class SampleController {

    @Autowired
    private TestService testService;
    @Autowired
    private TestManagement testManagement;
    @Autowired
    private RedisService redisService;

    @RequestMapping(value = "/sample", method = RequestMethod.GET)
    @ResponseBody
    public ApiResponse<String> sample() {
        return ApiResponse.success("ok");
    }

    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    public ApiResponse<Integer> test() throws ExecutionException, InterruptedException {
//        List<Test> tests = testService.get();
        BizTaskLogDO record = new BizTaskLogDO();
        record.setIsDelete(0L);
        record.setCreateDate(new Date());
        record.setTaskId(0L);
        record.setStartTime(new Date());
        record.setEndTime(new Date());
        record.setStatus(0);

       // taskInvokeService.invoke();

//        int result = bizTaskLogDAO.insert(record);
        return ApiResponse.success();
    }

    @RequestMapping(value = "/redis", method = RequestMethod.GET)
    @ResponseBody
    public ApiResponse<String> testRedis() {
//        String result = testService.testRedis();
        // 覆盖缓存
        redisService.set("1111111", "1111111", 500,TimeUnit.SECONDS);

        // 覆盖缓存
        redisService.set("1111111", "2222222", 500,TimeUnit.SECONDS);

        return ApiResponse.success();
    }

    @RequestMapping(value = "/exception")
    @ResponseBody
    public ApiResponse exceptionTest() {
        throw new BusinessException(new ExceptionDefinition("customErrorCode", "customErrorMessage"));
    }

    @RequestMapping(value = "/mq")
    @ResponseBody
    public ApiResponse mqTest(String msg) {
        testManagement.testMq(msg);
        return ApiResponse.success();
    }
    @RequestMapping(value = "/getById")
    @ResponseBody
    public ApiResponse getById(Long id) {
        return ApiResponse.success(testService.getById(id));
    }
}
