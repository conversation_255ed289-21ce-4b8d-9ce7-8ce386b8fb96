package cn.com.servyou.asynctask.web.controller;

import cn.com.servyou.asynctask.core.common.Mvel2ScriptService;
import cn.com.servyou.asynctask.core.domain.AsyncTaskCheckRetryExcDTO;
import cn.com.servyou.asynctask.core.domain.AsyncTaskUpdateAndRecordLogDTO;
import cn.com.servyou.asynctask.core.enums.TaskStatusEnum;
import cn.com.servyou.asynctask.core.service.BizTaskConfService;
import cn.com.servyou.asynctask.core.service.RedisService;
import cn.com.servyou.asynctask.dao.BizTaskConfDAO;
import cn.com.servyou.asynctask.dataobj.BizTaskConfDO;
import cn.com.servyou.asynctask.facade.TaskFacade;
import cn.com.servyou.asynctask.facade.dto.TaskReqDTO;
import cn.com.servyou.asynctask.facade.dto.TaskRespDTO;
import cn.com.servyou.asynctask.shared.AsyncTaskScheduleManagement;
import cn.com.servyou.i7boot.message.Message;
import cn.com.servyou.xqy.framework.rpc.facade.SingleResult;
import cn.com.servyou.xqy.framework.web.rest.ApiResponse;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * @Description Too late to explain
 * <AUTHOR>
 * @Date 2024/8/19 13:55
 */
@RestController
@RequestMapping("/task/test")
public class TaskTestController {
    @Autowired
    private BizTaskConfService bizTaskConfService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TaskFacade taskFacade;
    @Autowired
    private Mvel2ScriptService mvel2ScriptService;
    @Autowired
    private AsyncTaskScheduleManagement asyncTaskScheduleManagement;
    @Autowired
    private BizTaskConfDAO bizTaskConfDAO;

    /**
     * 查询配置
     *
     * @return
     */
    @PostMapping(value = "/findAll")
    public ApiResponse findAll() {
        return ApiResponse.success(bizTaskConfDAO.findAll());
    }
    /**
     * 查询配置
     *
     * @return
     */
    @PostMapping(value = "/queryConfigWithCache")
    public ApiResponse queryConfigWithCache(@RequestBody BizTaskConfDO bizTaskConfDO) {
        BizTaskConfDO result = null;
        if (StrUtil.isNotBlank(bizTaskConfDO.getTaskCode())) {
            result = bizTaskConfService.getBizTaskConfByTaskCode(bizTaskConfDO);
        } else {
            result = bizTaskConfService.getBizTaskConfByPartAndBizCode(bizTaskConfDO);
        }
        return ApiResponse.success(result);
    }

    /**
     * 查询配置
     *
     * @return
     */
    @PostMapping(value = "/queryConfigFromDb")
    public ApiResponse queryConfigFromDb(@RequestBody BizTaskConfDO bizTaskConfDO) {

        BizTaskConfDO result = null;
        if (StrUtil.isNotBlank(bizTaskConfDO.getTaskCode())) {
            result = bizTaskConfDAO.selectByTaskCode(bizTaskConfDO.getTaskCode());
        } else {
            result = bizTaskConfDAO.selectByBizPartCodeAndBizCode(bizTaskConfDO.getBizPartCode(), bizTaskConfDO.getBizCode());
        }
        return ApiResponse.success(result);
    }

    @PostMapping(value = "/updateConfig")
    public ApiResponse queryConfig(@RequestBody BizTaskConfDO bizTaskConfDO) {

        Boolean result = bizTaskConfService.updateBizTaskConf(bizTaskConfDO);
        if (!result) {
            return ApiResponse.error(Message.createMessage("UPDATE_FAILED", "更新失败"));
        }
        redisService.delByCacheKey(bizTaskConfDO.getBizPartCode());
        if (StrUtil.isNotBlank(bizTaskConfDO.getTaskCode())) {
            redisService.delByCacheKey(bizTaskConfDO.getTaskCode());
        }
        return ApiResponse.success(bizTaskConfDO);
    }

    @PostMapping(value = "/updateConfigOnlyCache")
    public ApiResponse updateConfigOnlyCache(@RequestBody BizTaskConfDO bizTaskConfDO) {
        if (StrUtil.isNotBlank(bizTaskConfDO.getBizPartCode())) {
            redisService.delByCacheKey(bizTaskConfDO.getBizPartCode());
        }
        if (StrUtil.isNotBlank(bizTaskConfDO.getTaskCode())) {
            redisService.delByCacheKey(bizTaskConfDO.getTaskCode());
        }
        return ApiResponse.success(bizTaskConfDO);
    }

    @PostMapping(value = "/createTask")
    public ApiResponse createTask(@RequestBody TaskReqDTO taskReqDTO) {
        SingleResult<TaskRespDTO> singleResult = taskFacade.createTask(taskReqDTO);
        return ApiResponse.success(singleResult);
    }

    @PostMapping(value = "/execMvel")
    public ApiResponse execMvel(@RequestBody AsyncTaskCheckRetryExcDTO asyncTaskCheckRetryExcDTO) {
        Boolean result = mvel2ScriptService.execute(asyncTaskCheckRetryExcDTO);
        return ApiResponse.success(result);
    }


    @GetMapping(value = "/recordLogAndUpdateStatus")
    public ApiResponse recordLogAndUpdateStatus() {
        // 更新状态并记录日志
        AsyncTaskUpdateAndRecordLogDTO recordLogDTO = new AsyncTaskUpdateAndRecordLogDTO();
        recordLogDTO.setBizTaskId(3l);
        recordLogDTO.setCustomerId("123123123");
        recordLogDTO.setTaskStatus(TaskStatusEnum.FAIL);
        recordLogDTO.setReason(
                "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaabbb");
        recordLogDTO.setStartTime(new Date());
        recordLogDTO.setEndTime(new Date());
        recordLogDTO.setBizPartCode("p");
        recordLogDTO.setBizCode("a");
        asyncTaskScheduleManagement.recordLogAndUpdateStatus(recordLogDTO);
        return ApiResponse.success();
    }



}
