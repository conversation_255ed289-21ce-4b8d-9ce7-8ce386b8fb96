package cn.com.servyou.asynctask.web.controlleradvice;

import cn.com.servyou.xqy.framework.web.rest.ApiResponse;
import cn.com.servyou.xqy.framework.web.rest.Message;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
* 测试异常拦截处理
*/
@ControllerAdvice(basePackages = "cn.com.servyou.asynctask.web.controller")
public class CustomExceptionHandler {
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ApiResponse<Exception> customException(final Exception e) {
        return ApiResponse.error(new Message("ERROR_CODE", e.getMessage()));
    }
}
